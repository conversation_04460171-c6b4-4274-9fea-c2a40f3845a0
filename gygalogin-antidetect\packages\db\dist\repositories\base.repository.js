"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRepository = void 0;
class BaseRepository {
    constructor(repository) {
        this.repository = repository;
    }
    async findById(id) {
        return this.repository.findOne({ where: { id } });
    }
    async findAll(options) {
        return this.repository.find(options);
    }
    async create(data) {
        const entity = this.repository.create(data);
        return this.repository.save(entity);
    }
    async update(id, data) {
        await this.repository.update(id, data);
        return this.findById(id);
    }
    async delete(id) {
        const result = await this.repository.delete(id);
        return result.affected ? result.affected > 0 : false;
    }
    async count(where) {
        return this.repository.count({ where });
    }
    async exists(where) {
        const count = await this.repository.count({ where });
        return count > 0;
    }
    async findBy(where, options) {
        return this.repository.find({ ...options, where });
    }
    async findOneBy(where) {
        return this.repository.findOne({ where });
    }
}
exports.BaseRepository = BaseRepository;
//# sourceMappingURL=base.repository.js.map