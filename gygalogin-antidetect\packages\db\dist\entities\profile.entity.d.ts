import { SessionEntity } from './session.entity';
import { ProxyEntity } from './proxy.entity';
export declare class ProfileEntity {
    id: string;
    name: string;
    description?: string;
    userAgent?: string;
    timezone?: string;
    language?: string;
    platform?: string;
    viewport?: {
        width: number;
        height: number;
        deviceScaleFactor: number;
        isMobile: boolean;
        hasTouch: boolean;
    };
    fingerprint?: {
        canvas: {
            noiseLevel: number;
            noiseType: 'subtle' | 'moderate' | 'aggressive';
            enabled: boolean;
        };
        webgl: {
            vendor: string;
            renderer: string;
            extensions: string[];
            parameters: Record<string, any>;
            enabled: boolean;
        };
        audio: {
            noiseLevel: number;
            enabled: boolean;
            context?: {
                sampleRate: number;
                maxChannelCount: number;
            };
        };
        fonts: {
            availableFonts: string[];
            enabled: boolean;
        };
        hardware: {
            memory: number;
            cores: number;
            platform: string;
            architecture: string;
        };
    };
    proxyId?: string;
    proxy?: ProxyEntity;
    tags?: string[];
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    lastUsed?: Date;
    sessions: SessionEntity[];
}
//# sourceMappingURL=profile.entity.d.ts.map