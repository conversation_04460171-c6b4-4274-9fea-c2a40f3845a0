"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScheduledTaskEntity = void 0;
const typeorm_1 = require("typeorm");
const workflow_entity_1 = require("./workflow.entity");
let ScheduledTaskEntity = class ScheduledTaskEntity {
    // Helper methods
    isTaskActive() {
        return this.status === 'active';
    }
    canRun() {
        return this.isTaskActive() && (!this.nextRunAt || this.nextRunAt <= new Date());
    }
    markAsRunning() {
        this.status = 'running';
        this.lastRunAt = new Date();
    }
    markAsCompleted() {
        this.status = 'active';
        this.runCount++;
        this.successCount++;
        this.lastError = undefined;
        this.updateNextRunTime();
    }
    markAsFailed(error) {
        this.status = 'failed';
        this.runCount++;
        this.failureCount++;
        this.lastError = error;
    }
    updateNextRunTime() {
        if (this.type === 'interval' && this.intervalMs) {
            this.nextRunAt = new Date(Date.now() + this.intervalMs);
        }
        else if (this.type === 'polling' && this.pollIntervalMs) {
            this.nextRunAt = new Date(Date.now() + this.pollIntervalMs);
        }
        else if (this.type === 'cron' && (this.cronExpression || this.cronTime)) {
            // For cron, we would use a cron parser to calculate next run time
            // For now, we'll set it to null and handle it in the service layer
            this.nextRunAt = undefined;
        }
    }
};
exports.ScheduledTaskEntity = ScheduledTaskEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ScheduledTaskEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ScheduledTaskEntity.prototype, "workflowId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => workflow_entity_1.WorkflowEntity, { onDelete: 'CASCADE' }),
    __metadata("design:type", workflow_entity_1.WorkflowEntity)
], ScheduledTaskEntity.prototype, "workflow", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ScheduledTaskEntity.prototype, "nodeName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ScheduledTaskEntity.prototype, "cronTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'simple-json', nullable: true }),
    __metadata("design:type", Object)
], ScheduledTaskEntity.prototype, "triggerTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], ScheduledTaskEntity.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], ScheduledTaskEntity.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ScheduledTaskEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        default: 'cron',
    }),
    __metadata("design:type", String)
], ScheduledTaskEntity.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        default: 'inactive',
    }),
    __metadata("design:type", String)
], ScheduledTaskEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], ScheduledTaskEntity.prototype, "cronExpression", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], ScheduledTaskEntity.prototype, "intervalMs", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500, nullable: true }),
    __metadata("design:type", String)
], ScheduledTaskEntity.prototype, "pollUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], ScheduledTaskEntity.prototype, "pollIntervalMs", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], ScheduledTaskEntity.prototype, "nextRunAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], ScheduledTaskEntity.prototype, "lastRunAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], ScheduledTaskEntity.prototype, "runCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], ScheduledTaskEntity.prototype, "successCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], ScheduledTaskEntity.prototype, "failureCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ScheduledTaskEntity.prototype, "lastError", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'simple-json', nullable: true }),
    __metadata("design:type", Object)
], ScheduledTaskEntity.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ScheduledTaskEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ScheduledTaskEntity.prototype, "updatedAt", void 0);
exports.ScheduledTaskEntity = ScheduledTaskEntity = __decorate([
    (0, typeorm_1.Entity)('scheduled_tasks'),
    (0, typeorm_1.Index)(['workflowId']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['nextRunAt'])
], ScheduledTaskEntity);
//# sourceMappingURL=scheduled-task.entity.js.map