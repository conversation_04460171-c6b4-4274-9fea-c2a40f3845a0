"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GraphRunner = void 0;
const p_cancelable_1 = require("p-cancelable");
const workflow_state_manager_1 = require("./workflow-state-manager");
const node_executor_1 = require("./node-executor");
class GraphRunner {
    constructor(workflow, stateManager, context, hooks, abortSignal) {
        this.workflow = workflow;
        this.stateManager = stateManager;
        this.context = context;
        this.hooks = hooks;
        this.abortSignal = abortSignal;
        this.executionQueue = [];
        this.multiInputNodes = new Set();
        this.nodeExecutor = new node_executor_1.NodeExecutor(workflow, stateManager, abortSignal);
        this.precomputeMultiInputNodes();
    }
    run() {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            // Find and add start node to execution queue
            const startNode = this.workflow.findStartNode();
            if (!startNode) {
                throw new Error('Workflow has no start node.');
            }
            this.addToQueue(startNode, []);
            // Start execution tracking
            this.stateManager.startExecution();
            try {
                // Process execution queue
                while (this.executionQueue.length > 0) {
                    // Check for cancellation
                    if ((_a = this.abortSignal) === null || _a === void 0 ? void 0 : _a.aborted) {
                        throw new p_cancelable_1.CancelError('Workflow execution was canceled.');
                    }
                    const queueItem = this.executionQueue.shift();
                    if (!queueItem)
                        continue;
                    yield this.processQueueItem(queueItem);
                }
                console.log('Workflow execution finished.');
            }
            finally {
                // End execution tracking
                this.stateManager.endExecution();
            }
        });
    }
    processQueueItem(queueItem) {
        return __awaiter(this, void 0, void 0, function* () {
            const { node, inputData, options } = queueItem;
            try {
                // Run before node execute hook
                this.hooks.runHook('nodeExecuteBefore', [node]);
                // Execute the node
                const outputData = yield this.nodeExecutor.executeNode(node, inputData, this.context, options);
                // Run after node execute hook
                this.hooks.runHook('nodeExecuteAfter', [node, outputData]);
                // Apply paired items to output data
                const outputDataWithPairedItems = node_executor_1.NodeExecutor.applyPairedItem(inputData, outputData);
                // Store output in context as well (for backwards compatibility)
                this.context.setNodeOutput(node.id, outputDataWithPairedItems);
                // Process node outputs to determine next nodes to execute
                yield this.processNodeOutput(node.id, outputDataWithPairedItems);
            }
            catch (error) {
                if (error instanceof p_cancelable_1.CancelError) {
                    throw error;
                }
                // Run error hook
                this.hooks.runHook('onNodeError', [node, error]);
                // Handle error based on workflow settings
                yield this.handleNodeError(node, error, inputData);
            }
        });
    }
    handleNodeError(node, error, inputData) {
        return __awaiter(this, void 0, void 0, function* () {
            console.error(`Error executing node ${node.name} (${node.id}):`, error);
            // Check for error output connections
            const errorNodes = this.workflow.findNextNodes(node.id, 'error');
            if (errorNodes.length > 0) {
                // Create error data for error nodes
                const errorData = [{
                        json: {
                            error: error.message,
                            stack: error.stack,
                            sourceNodeId: node.id,
                            sourceNodeName: node.name,
                            originalInputData: inputData,
                        },
                        pairedItem: { item: 0 },
                    }];
                // Add error nodes to execution queue
                for (const errorNode of errorNodes) {
                    this.addToQueue(errorNode, errorData);
                }
            }
            else if (this.workflow.settings.errorBehaviour === 'stop') {
                // Stop execution if no error handling and error behavior is 'stop'
                throw error;
            }
            // If errorBehaviour is 'continue', just continue with next nodes
        });
    }
    processNodeOutput(sourceNodeId, outputData) {
        return __awaiter(this, void 0, void 0, function* () {
            const connections = this.workflow.connections.filter(c => c.sourceNodeId === sourceNodeId);
            for (const conn of connections) {
                // Skip error connections (handled separately)
                if (conn.sourceOutput === 'error') {
                    continue;
                }
                const nextNode = this.workflow.getNode(conn.targetNodeId);
                if (!nextNode) {
                    console.warn(`Target node ${conn.targetNodeId} not found for connection from ${sourceNodeId}`);
                    continue;
                }
                // Handle single-input nodes directly
                if (!this.multiInputNodes.has(nextNode.id)) {
                    this.addToQueue(nextNode, outputData);
                    continue;
                }
                // Handle multi-input nodes through state manager
                yield this.handleMultiInputNode(nextNode, conn.targetInput, outputData);
            }
        });
    }
    handleMultiInputNode(node, inputName, data) {
        return __awaiter(this, void 0, void 0, function* () {
            // Get required inputs for this node
            const requiredInputs = this.workflow.connections
                .filter(c => c.targetNodeId === node.id && c.sourceOutput !== 'error')
                .map(c => c.targetInput);
            // Initialize waiting node if not already done
            if (!this.stateManager.isNodeWaiting(node.id)) {
                this.stateManager.initializeWaitingNode(node.id, requiredInputs);
            }
            // Add input data to waiting node
            const allInputsReceived = this.stateManager.addNodeInput(node.id, inputName, data);
            // If all inputs are received, add to execution queue
            if (allInputsReceived) {
                const combinedInputData = this.stateManager.getWaitingNodeInputs(node.id);
                this.stateManager.removeWaitingNode(node.id);
                this.addToQueue(node, combinedInputData);
            }
        });
    }
    addToQueue(node, inputData, options) {
        // Skip disabled nodes
        if (node.disabled) {
            this.stateManager.setNodeStatus(node.id, workflow_state_manager_1.NodeExecutionStatus.SKIPPED);
            return;
        }
        this.executionQueue.push({
            node,
            inputData,
            options,
        });
    }
    precomputeMultiInputNodes() {
        const allNodes = this.workflow.getAllNodes();
        const inputCounts = {};
        // Initialize input counts
        for (const node of allNodes) {
            inputCounts[node.id] = 0;
        }
        // Count inputs for each node (excluding error connections)
        for (const conn of this.workflow.connections) {
            if (conn.sourceOutput !== 'error' && inputCounts[conn.targetNodeId] !== undefined) {
                inputCounts[conn.targetNodeId]++;
            }
        }
        // Mark nodes with multiple inputs
        for (const nodeId in inputCounts) {
            if (inputCounts[nodeId] > 1) {
                this.multiInputNodes.add(nodeId);
            }
        }
    }
    // Utility methods
    getExecutionQueue() {
        return [...this.executionQueue];
    }
    getQueueLength() {
        return this.executionQueue.length;
    }
    clearQueue() {
        this.executionQueue = [];
    }
    isQueueEmpty() {
        return this.executionQueue.length === 0;
    }
    // Method to add nodes to queue manually (for testing or special cases)
    enqueueNode(node, inputData, options) {
        this.addToQueue(node, inputData, options);
    }
    // Method to get multi-input nodes (for debugging)
    getMultiInputNodes() {
        return new Set(this.multiInputNodes);
    }
}
exports.GraphRunner = GraphRunner;
//# sourceMappingURL=graph-runner.js.map