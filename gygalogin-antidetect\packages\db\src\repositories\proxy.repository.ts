import { Service } from '@gygalogin/decorators';
import { DataSource, MoreThan, Repository } from 'typeorm';
import { ProxyEntity } from '../entities/proxy.entity';

export interface ProxySearchCriteria {
  type?: 'http' | 'https' | 'socks4' | 'socks5';
  country?: string;
  city?: string;
  enabled?: boolean;
  minHealthScore?: number;
  isBlacklisted?: boolean;
}

@Service()
export class ProxyRepository extends Repository<ProxyEntity> {
  constructor(dataSource: DataSource) {
    super(ProxyEntity, dataSource.manager);
  }

  /**
   * Find entity by ID - added for compatibility with core package
   */
  async findById(id: string): Promise<ProxyEntity | null> {
    return this.findOne({ where: { id } as any });
  }

  /**
   * Create a new proxy entity and return it
   */
  async createProxy(proxyData: Partial<ProxyEntity>): Promise<ProxyEntity> {
    const proxy = this.create(proxyData);
    return this.save(proxy);
  }

  /**
   * Update proxy and return the updated entity
   */
  async updateProxy(id: string, updateData: Partial<ProxyEntity>): Promise<ProxyEntity | null> {
    await this.update(id, updateData);
    return this.findOneBy({ id } as any);
  }

  /**
   * Delete proxy by id and return success status
   */
  async deleteProxy(id: string): Promise<boolean> {
    const result = await this.delete(id);
    return (result.affected ?? 0) > 0;
  }

  /**
   * Find all proxies
   */
  async findAll(): Promise<ProxyEntity[]> {
    return this.find();
  }

  /**
   * Find enabled proxies
   */
  async findEnabled(): Promise<ProxyEntity[]> {
    return this.findBy({ enabled: true });
  }

  /**
   * Find proxies by type
   */
  async findByType(type: 'http' | 'https' | 'socks4' | 'socks5'): Promise<ProxyEntity[]> {
    return this.findBy({ type });
  }

  /**
   * Find proxies by country
   */
  async findByCountry(country: string): Promise<ProxyEntity[]> {
    return this.findBy({ country });
  }

  /**
   * Find proxies by host
   */
  async findByHost(host: string): Promise<ProxyEntity[]> {
    return this.findBy({ host });
  }

  /**
   * Find healthy proxies with minimum health score
   */
  async findHealthy(minHealthScore: number = 70): Promise<ProxyEntity[]> {
    return this.find({
      where: {
        healthScore: MoreThan(minHealthScore),
        enabled: true,
      },
    });
  }

  /**
   * Find non-blacklisted proxies
   */
  async findNonBlacklisted(): Promise<ProxyEntity[]> {
    const now = new Date();
    return this.createQueryBuilder('proxy')
      .where('proxy.enabled = :enabled', { enabled: true })
      .andWhere('(proxy.blacklistedUntil IS NULL OR proxy.blacklistedUntil <= :now)', { now })
      .getMany();
  }

  /**
   * Search proxies with multiple criteria
   */
  async search(criteria: ProxySearchCriteria): Promise<ProxyEntity[]> {
    const queryBuilder = this.createQueryBuilder('proxy');

    if (criteria.type) {
      queryBuilder.andWhere('proxy.type = :type', { type: criteria.type });
    }

    if (criteria.country) {
      queryBuilder.andWhere('proxy.country = :country', { country: criteria.country });
    }

    if (criteria.city) {
      queryBuilder.andWhere('proxy.city = :city', { city: criteria.city });
    }

    if (criteria.enabled !== undefined) {
      queryBuilder.andWhere('proxy.enabled = :enabled', { enabled: criteria.enabled });
    }

    if (criteria.minHealthScore !== undefined) {
      queryBuilder.andWhere('proxy.healthScore >= :minHealthScore', {
        minHealthScore: criteria.minHealthScore,
      });
    }

    if (criteria.isBlacklisted === false) {
      const now = new Date();
      queryBuilder.andWhere('(proxy.blacklistedUntil IS NULL OR proxy.blacklistedUntil <= :now)', {
        now,
      });
    } else if (criteria.isBlacklisted === true) {
      const now = new Date();
      queryBuilder.andWhere('proxy.blacklistedUntil > :now', { now });
    }

    return queryBuilder.getMany();
  }

  /**
   * Get the best available proxy based on health score and blacklist status
   */
  async getBestAvailable(
    type?: 'http' | 'https' | 'socks4' | 'socks5'
  ): Promise<ProxyEntity | null> {
    const queryBuilder = this.createQueryBuilder('proxy')
      .where('proxy.enabled = :enabled', { enabled: true })
      .andWhere('(proxy.blacklistedUntil IS NULL OR proxy.blacklistedUntil <= :now)', {
        now: new Date(),
      })
      .orderBy('proxy.healthScore', 'DESC')
      .limit(1);

    if (type) {
      queryBuilder.andWhere('proxy.type = :type', { type });
    }

    return queryBuilder.getOne();
  }

  /**
   * Update health score
   */
  async updateHealthScore(id: string, healthScore: number): Promise<void> {
    await this.update(id, {
      healthScore,
      lastChecked: new Date(),
    });
  }

  /**
   * Blacklist a proxy until a specific date
   */
  async blacklist(id: string, until: Date): Promise<void> {
    await this.update(id, { blacklistedUntil: until });
  }

  /**
   * Remove blacklist from a proxy
   */
  async removeBlacklist(id: string): Promise<void> {
    await this.query('UPDATE proxies SET blacklistedUntil = NULL WHERE id = ?', [id]);
  }

  /**
   * Find proxies with their associated profiles
   */
  async findWithProfiles(id?: string): Promise<ProxyEntity | ProxyEntity[]> {
    const query = this.createQueryBuilder('proxy').leftJoinAndSelect('proxy.profiles', 'profile');

    if (id) {
      query.where('proxy.id = :id', { id });
      return query.getOne() as Promise<ProxyEntity>;
    }

    return query.getMany();
  }

  /**
   * Get proxy statistics
   */
  async getStats(): Promise<{
    total: number;
    enabled: number;
    disabled: number;
    healthy: number; // health score >= 70
    unhealthy: number; // health score < 70
    blacklisted: number;
    byType: Record<string, number>;
    byCountry: Record<string, number>;
  }> {
    const total = await this.count();
    const enabled = await this.countBy({ enabled: true });
    const disabled = total - enabled;

    const healthy = await this.createQueryBuilder('proxy')
      .where('proxy.healthScore >= :minScore', { minScore: 70 })
      .getCount();

    const unhealthy = total - healthy;

    const now = new Date();
    const blacklisted = await this.createQueryBuilder('proxy')
      .where('proxy.blacklistedUntil > :now', { now })
      .getCount();

    // Get stats by type
    const typeStats = await this.createQueryBuilder('proxy')
      .select('proxy.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('proxy.type')
      .getRawMany();

    const byType = typeStats.reduce(
      (acc, stat) => {
        acc[stat.type] = parseInt(stat.count);
        return acc;
      },
      {} as Record<string, number>
    );

    // Get stats by country
    const countryStats = await this.createQueryBuilder('proxy')
      .select('proxy.country', 'country')
      .addSelect('COUNT(*)', 'count')
      .where('proxy.country IS NOT NULL')
      .groupBy('proxy.country')
      .getRawMany();

    const byCountry = countryStats.reduce(
      (acc, stat) => {
        if (stat.country) {
          acc[stat.country] = parseInt(stat.count);
        }
        return acc;
      },
      {} as Record<string, number>
    );

    return {
      total,
      enabled,
      disabled,
      healthy,
      unhealthy,
      blacklisted,
      byType,
      byCountry,
    };
  }
}
