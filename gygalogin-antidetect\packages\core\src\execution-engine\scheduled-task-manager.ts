import { Logger } from '@gygalogin/shared';
import * as cron from 'node-cron';
import { Workflow } from '@gygalogin/shared';
import {
  CronExpression,
  TriggerTime,
  toCronExpression,
  validateCronExpression,
  getDisplayableCron,
} from '../common/cron-expressions';
import { 
  ScheduledTaskRepository, 
  ScheduledTaskEntity, 
  TaskStatus, 
  TaskType 
} from '@gygalogin/db';

export interface CronJobEntry {
  job: cron.ScheduledTask;
  displayableCron: string;
  triggerTime: TriggerTime;
}

export interface ScheduleOptions {
  cron?: string;
  interval?: number;
  pollUrl?: string;
  pollInterval?: number;
}

export interface TaskMetadata {
  name?: string;
  description?: string;
}

export class ScheduledTaskManager {
  private static instance: ScheduledTaskManager;
  readonly cronMap = new Map<string, Array<CronJobEntry>>();
  private logger: Logger;
  private taskRepository?: ScheduledTaskRepository;

  constructor(taskRepository?: ScheduledTaskRepository) {
    this.logger = new Logger();
    this.taskRepository = taskRepository;
  }

  public static getInstance(): ScheduledTaskManager {
    if (!ScheduledTaskManager.instance) {
      ScheduledTaskManager.instance = new ScheduledTaskManager();
    }
    return ScheduledTaskManager.instance;
  }

  public static getInstanceWithRepository(taskRepository: ScheduledTaskRepository): ScheduledTaskManager {
    if (!ScheduledTaskManager.instance) {
      ScheduledTaskManager.instance = new ScheduledTaskManager(taskRepository);
    } else {
      ScheduledTaskManager.instance.taskRepository = taskRepository;
    }
    return ScheduledTaskManager.instance;
  }

  public setRepository(taskRepository: ScheduledTaskRepository): void {
    this.taskRepository = taskRepository;
  }

  /**
   * Register a cron job for a workflow
   * @param workflow The workflow to register the cron for
   * @param triggerTime The trigger time configuration
   * @param onTick The function to execute when the cron triggers
   */
  registerCron(workflow: Workflow, triggerTime: TriggerTime, onTick: () => void) {
    const cronExpression: CronExpression = toCronExpression(triggerTime);

    // Validate the cron expression
    if (!validateCronExpression(cronExpression)) {
      throw new Error('The polling interval is too short. It has to be at least a minute.');
    }

    const displayableCron = getDisplayableCron(cronExpression, triggerTime);

    this.logger.debug('Registering cron for workflow', {
      workflowId: workflow.id,
      workflowName: workflow.name,
      cron: displayableCron,
    });

    const cronJob = cron.schedule(
      cronExpression,
      () => {
        this.logger.debug('Executing cron for workflow', {
          workflowId: workflow.id,
          workflowName: workflow.name,
          cron: displayableCron,
        });
        onTick();
      },
      {
        scheduled: true,
        timezone: workflow.settings.timezone || 'UTC',
      }
    );

    const workflowCronEntries = this.cronMap.get(workflow.id);
    const cronEntry: CronJobEntry = {
      job: cronJob,
      displayableCron,
      triggerTime,
    };

    if (workflowCronEntries) {
      workflowCronEntries.push(cronEntry);
    } else {
      this.cronMap.set(workflow.id, [cronEntry]);
    }

    this.logger.info('Registered cron for workflow', {
      workflowId: workflow.id,
      workflowName: workflow.name,
      cron: displayableCron,
    });
  }

  /**
   * Deregister all cron jobs for a workflow
   * @param workflowId The ID of the workflow to deregister crons for
   */
  deregisterCrons(workflowId: string) {
    const cronJobs = this.cronMap.get(workflowId) ?? [];

    if (cronJobs.length === 0) {
      this.logger.debug('No crons to deregister for workflow', { workflowId });
      return;
    }

    const crons: string[] = [];

    while (cronJobs.length) {
      const cronEntry = cronJobs.pop();
      if (cronEntry) {
        crons.push(cronEntry.displayableCron);
        cronEntry.job.stop();
      }
    }

    this.cronMap.delete(workflowId);

    this.logger.info('Deregistered all crons for workflow', {
      workflowId,
      crons,
    });
  }

  /**
   * Deregister all cron jobs
   */
  deregisterAllCrons() {
    for (const workflowId of this.cronMap.keys()) {
      this.deregisterCrons(workflowId);
    }
    this.logger.info('Deregistered all cron jobs');
  }

  /**
   * Get information about active crons
   */
  getActiveCrons(): Record<string, string[]> {
    const activeCrons: Record<string, string[]> = {};
    for (const [workflowId, cronJobs] of this.cronMap) {
      activeCrons[`workflow-${workflowId}`] = cronJobs.map(
        ({ displayableCron }) => displayableCron
      );
    }
    return activeCrons;
  }

  // Enhanced methods for CLI and API integration
  public async scheduleWorkflow(
    workflowId: string, 
    options: ScheduleOptions, 
    metadata?: TaskMetadata
  ): Promise<string> {
    if (!this.taskRepository) {
      throw new Error('ScheduledTaskRepository not initialized');
    }

    try {
      // Determine task type and configuration
      let taskType: TaskType;
      let cronExpression: string | undefined;
      let intervalMs: number | undefined;
      let pollUrl: string | undefined;
      let pollIntervalMs: number | undefined;
      let nextRunAt: Date | undefined;

      if (options.cron) {
        taskType = 'cron';
        cronExpression = options.cron;
        // For cron tasks, we'll calculate next run time in the entity helper methods
      } else if (options.interval) {
        taskType = 'interval';
        intervalMs = options.interval;
        nextRunAt = new Date(Date.now() + options.interval);
      } else if (options.pollUrl) {
        taskType = 'polling';
        pollUrl = options.pollUrl;
        pollIntervalMs = options.pollInterval || 30000;
        nextRunAt = new Date(Date.now() + pollIntervalMs);
      } else {
        throw new Error('Invalid schedule options: must specify cron, interval, or pollUrl');
      }

      // Create the scheduled task in database
      const task = await this.taskRepository.create({
        workflowId,
        name: metadata?.name,
        description: metadata?.description,
        type: taskType,
        status: 'inactive', // Start inactive, will be activated separately
        cronExpression,
        intervalMs,
        pollUrl,
        pollIntervalMs,
        nextRunAt,
        runCount: 0,
        successCount: 0,
        failureCount: 0,
      });

      this.logger.info('Scheduled workflow in database', {
        workflowId,
        taskId: task.id,
        type: taskType,
        options,
        metadata
      });

      return task.id;
    } catch (error) {
      this.logger.error('Failed to schedule workflow', {
        workflowId,
        options,
        error: (error as Error).message
      });
      throw error;
    }
  }

  public async activateTask(taskId: string): Promise<void> {
    if (!this.taskRepository) {
      throw new Error('ScheduledTaskRepository not initialized');
    }

    try {
      const task = await this.taskRepository.findById(taskId);
      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      await this.taskRepository.update(taskId, {
        status: 'active',
        nextRunAt: this.calculateNextRunTime(task)
      });

      this.logger.info('Activated task', { taskId, workflowId: task.workflowId });
    } catch (error) {
      this.logger.error('Failed to activate task', {
        taskId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  public async deactivateTask(taskId: string): Promise<void> {
    if (!this.taskRepository) {
      throw new Error('ScheduledTaskRepository not initialized');
    }

    try {
      const task = await this.taskRepository.findById(taskId);
      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      await this.taskRepository.update(taskId, {
        status: 'inactive',
        nextRunAt: undefined
      });

      this.logger.info('Deactivated task', { taskId, workflowId: task.workflowId });
    } catch (error) {
      this.logger.error('Failed to deactivate task', {
        taskId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  public async deleteTask(taskId: string): Promise<void> {
    if (!this.taskRepository) {
      throw new Error('ScheduledTaskRepository not initialized');
    }

    try {
      const task = await this.taskRepository.findById(taskId);
      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      await this.taskRepository.delete(taskId);

      this.logger.info('Deleted task', { taskId, workflowId: task.workflowId });
    } catch (error) {
      this.logger.error('Failed to delete task', {
        taskId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  public async createPollingTask(
    workflowId: string, 
    pollUrl: string, 
    pollInterval: number, 
    metadata?: TaskMetadata
  ): Promise<string> {
    return this.scheduleWorkflow(workflowId, {
      pollUrl,
      pollInterval
    }, metadata);
  }

  public async listTasks(options?: {
    workflowId?: string;
    status?: TaskStatus;
    type?: TaskType;
    limit?: number;
    offset?: number;
  }): Promise<ScheduledTaskEntity[]> {
    if (!this.taskRepository) {
      throw new Error('ScheduledTaskRepository not initialized');
    }

    try {
      const searchCriteria: any = {};
      if (options?.workflowId) searchCriteria.workflowId = options.workflowId;
      if (options?.status) searchCriteria.status = options.status;

      let tasks: ScheduledTaskEntity[];
      
      if (Object.keys(searchCriteria).length > 0) {
        // Use search if we have criteria
        tasks = await this.taskRepository.searchTasks({
          workflowId: searchCriteria.workflowId,
          isActive: searchCriteria.status === 'active'
        });
      } else {
        // Get all tasks
        tasks = await this.taskRepository.findAll({
          take: options?.limit,
          skip: options?.offset,
          order: { createdAt: 'DESC' }
        });
      }

      // Filter by type if specified
      if (options?.type) {
        tasks = tasks.filter(task => task.type === options.type);
      }

      this.logger.debug('Listed tasks', { count: tasks.length, options });
      return tasks;
    } catch (error) {
      this.logger.error('Failed to list tasks', {
        options,
        error: (error as Error).message
      });
      throw error;
    }
  }

  public async getTask(taskId: string): Promise<ScheduledTaskEntity | null> {
    if (!this.taskRepository) {
      throw new Error('ScheduledTaskRepository not initialized');
    }

    try {
      const task = await this.taskRepository.findById(taskId);
      this.logger.debug('Retrieved task', { taskId, found: !!task });
      return task;
    } catch (error) {
      this.logger.error('Failed to get task', {
        taskId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  public async getTaskExecutions(taskId: string, options?: {
    limit?: number;
    offset?: number;
    status?: 'running' | 'success' | 'error' | 'canceled';
  }): Promise<any[]> {
    // This would integrate with ExecutionRepository to get executions for this task's workflow
    // For now, we'll return empty array but log the request
    this.logger.debug('Getting task executions', { taskId, options });
    
    try {
      const task = await this.getTask(taskId);
      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      // TODO: Integrate with ExecutionRepository to fetch actual executions
      // const executions = await executionRepository.findByWorkflowId(task.workflowId, options?.limit);
      
      return [];
    } catch (error) {
      this.logger.error('Failed to get task executions', {
        taskId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  public async triggerTask(taskId: string): Promise<string> {
    if (!this.taskRepository) {
      throw new Error('ScheduledTaskRepository not initialized');
    }

    try {
      const task = await this.taskRepository.findById(taskId);
      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      // Update task status to running
      task.markAsRunning();
      await this.taskRepository.update(taskId, {
        status: task.status,
        lastRunAt: task.lastRunAt
      });

      // Generate execution ID (would integrate with WorkflowExecute)
      const executionId = `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      this.logger.info('Triggered task', { 
        taskId, 
        workflowId: task.workflowId,
        executionId 
      });
      
      return executionId;
    } catch (error) {
      this.logger.error('Failed to trigger task', {
        taskId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Calculate next run time for a task based on its type and configuration
   */
  private calculateNextRunTime(task: ScheduledTaskEntity): Date | undefined {
    const now = new Date();
    
    switch (task.type) {
      case 'interval':
        return task.intervalMs ? new Date(now.getTime() + task.intervalMs) : undefined;
      
      case 'polling':
        return task.pollIntervalMs ? new Date(now.getTime() + task.pollIntervalMs) : undefined;
      
      case 'cron':
        // For cron tasks, we would use a cron parser to calculate next run time
        // For now, return undefined and handle in service layer
        return undefined;
      
      default:
        return undefined;
    }
  }

  /**
   * Mark a task as completed successfully
   */
  public async markTaskCompleted(taskId: string): Promise<void> {
    if (!this.taskRepository) {
      throw new Error('ScheduledTaskRepository not initialized');
    }

    try {
      const task = await this.taskRepository.findById(taskId);
      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      task.markAsCompleted();
      await this.taskRepository.update(taskId, {
        status: task.status,
        runCount: task.runCount,
        successCount: task.successCount,
        lastError: task.lastError,
        nextRunAt: task.nextRunAt
      });

      this.logger.info('Marked task as completed', { taskId });
    } catch (error) {
      this.logger.error('Failed to mark task as completed', {
        taskId,
        error: (error as Error).message
      });
      throw error;
    }
  }

  /**
   * Mark a task as failed
   */
  public async markTaskFailed(taskId: string, errorMessage: string): Promise<void> {
    if (!this.taskRepository) {
      throw new Error('ScheduledTaskRepository not initialized');
    }

    try {
      const task = await this.taskRepository.findById(taskId);
      if (!task) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      task.markAsFailed(errorMessage);
      await this.taskRepository.update(taskId, {
        status: task.status,
        runCount: task.runCount,
        failureCount: task.failureCount,
        lastError: task.lastError
      });

      this.logger.info('Marked task as failed', { taskId, errorMessage });
    } catch (error) {
      this.logger.error('Failed to mark task as failed', {
        taskId,
        error: (error as Error).message
      });
      throw error;
    }
  }
}
