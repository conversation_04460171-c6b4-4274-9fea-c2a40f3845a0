import { WorkflowEntity } from './workflow.entity';
export declare enum ExecutionStatus {
    WAITING = "waiting",
    RUNNING = "running",
    SUCCESS = "success",
    ERROR = "error",
    CANCELED = "canceled",
    CRASHED = "crashed"
}
export declare enum ExecutionMode {
    MANUAL = "manual",
    TRIGGER = "trigger",
    WEBHOOK = "webhook",
    POLLING = "polling",
    CLI = "cli",
    ERROR_TRIGGER = "error_trigger",
    INTERNAL = "internal"
}
export declare class ExecutionEntity {
    id: string;
    workflowId: string;
    workflow: WorkflowEntity;
    status: ExecutionStatus;
    mode: ExecutionMode;
    startedAt?: Date;
    stoppedAt?: Date;
    finishedAt?: Date;
    data?: {
        resultData?: {
            runData: Record<string, Array<{
                startTime: number;
                executionTime: number;
                data: {
                    main: Array<{
                        json: Record<string, any>;
                        binary?: Record<string, any>;
                        pairedItem?: {
                            item: number;
                        };
                    }>;
                };
                source?: Array<{
                    previousNode: string;
                    previousNodeOutput?: number;
                    previousNodeRun?: number;
                }>;
            }>>;
            pinData?: Record<string, any>;
            lastNodeExecuted?: string;
        };
        executionData?: {
            contextData: Record<string, any>;
            nodeExecutionStack: Array<{
                node: {
                    id: string;
                    name: string;
                    type: string;
                    typeVersion: number;
                    parameters: Record<string, any>;
                };
                data: Record<string, any>;
            }>;
            metadata?: Record<string, any>;
            waitingExecution?: Record<string, any>;
            waitingExecutionSource?: Record<string, any>;
        };
    };
    workflowData?: {
        id: string;
        name: string;
        nodes: Array<{
            id: string;
            name: string;
            type: string;
            typeVersion: number;
            position: [number, number];
            parameters: Record<string, any>;
        }>;
        connections: Array<{
            sourceNodeId: string;
            sourceOutput: string;
            targetNodeId: string;
            targetInput: string;
        }>;
        settings?: Record<string, any>;
        staticData?: Record<string, any>;
    };
    retryOf?: string;
    retrySuccessId?: number;
    error?: string;
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
}
//# sourceMappingURL=execution.entity.d.ts.map