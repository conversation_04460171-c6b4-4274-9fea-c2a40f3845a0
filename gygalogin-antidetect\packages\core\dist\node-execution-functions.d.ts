import { IExecuteFunctions, INode, INodeExecutionData, IExecutionContext } from '@gygalogin/shared';
import { WorkflowNodeContext } from './execution-engine/node-execution-context/workflow-node-context';
interface ICredentialDataDecryptedObject {
    [key: string]: any;
}
export declare class NodeExecuteFunctions implements IExecuteFunctions {
    helpers: {
        [key: string]: any;
    };
    private context;
    private node;
    private inputData;
    constructor(context: WorkflowNodeContext, node: INode, inputData: INodeExecutionData[]);
    getNodeParameter(name: string, defaultValue?: any): any;
    getNodeParameter(name: string, index: number, defaultValue?: any): any;
    getNodeParameter(name: string, itemIndex: number, defaultValue?: any): any;
    getContext(): IExecutionContext;
    getSharedData(key?: string): any;
    setSharedData(key: string, value: any): void;
    getExecutionInfo(): {
        executionId?: string;
        workflowId?: string;
        runMode?: string;
    };
    getInputData(): INodeExecutionData[];
    getInputData(itemIndex: number): INodeExecutionData;
    getInputDataFrom(nodeName: string): INodeExecutionData[];
    getCredentials(name: string): Promise<ICredentialDataDecryptedObject>;
    evaluateComparison(_operator: string, _value1: any, _value2: any): boolean;
    getNode(): INode;
}
export {};
//# sourceMappingURL=node-execution-functions.d.ts.map