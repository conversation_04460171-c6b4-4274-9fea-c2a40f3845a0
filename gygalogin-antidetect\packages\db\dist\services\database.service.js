"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const decorators_1 = require("@gygalogin/decorators");
const shared_1 = require("@gygalogin/shared");
const connection_1 = require("../connection");
let DatabaseService = class DatabaseService {
    constructor(logger) {
        this.logger = logger;
        this.dataSource = null;
    }
    async initialize() {
        if (this.dataSource?.isInitialized) {
            return this.dataSource;
        }
        try {
            this.dataSource = await connection_1.AppDataSource.initialize();
            this.logger.info('✅ Database connection established');
            return this.dataSource;
        }
        catch (error) {
            this.logger.error('❌ Database connection failed:', error);
            throw error;
        }
    }
    async close() {
        if (this.dataSource?.isInitialized) {
            await this.dataSource.destroy();
            this.logger.info('🔌 Database connection closed');
        }
    }
    getDataSource() {
        if (!this.dataSource?.isInitialized) {
            throw new Error('Database not initialized. Call initialize() first.');
        }
        return this.dataSource;
    }
    async isHealthy() {
        try {
            if (!this.dataSource?.isInitialized)
                return false;
            await this.dataSource.query('SELECT 1');
            return true;
        }
        catch {
            return false;
        }
    }
};
exports.DatabaseService = DatabaseService;
exports.DatabaseService = DatabaseService = __decorate([
    (0, decorators_1.Service)(),
    __metadata("design:paramtypes", [shared_1.Logger])
], DatabaseService);
//# sourceMappingURL=database.service.js.map