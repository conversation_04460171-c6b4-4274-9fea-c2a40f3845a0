import { BaseError } from '@gygalogin/shared';
/**
 * Base class for all node execution errors
 */
export declare class NodeExecutionError extends BaseError {
    readonly nodeId: string;
    readonly nodeType?: string | undefined;
    readonly context?: Record<string, any> | undefined;
    readonly attempt?: number | undefined;
    readonly maxAttempts?: number | undefined;
    constructor(message: string, nodeId: string, nodeType?: string | undefined, context?: Record<string, any> | undefined, attempt?: number | undefined, maxAttempts?: number | undefined);
}
/**
 * Error thrown when node execution times out
 */
export declare class NodeTimeoutError extends BaseError {
    readonly nodeId: string;
    readonly timeout: number;
    readonly nodeType?: string | undefined;
    readonly attempt?: number | undefined;
    readonly context: Record<string, any>;
    constructor(nodeId: string, timeout: number, nodeType?: string | undefined, context?: Record<string, any>, attempt?: number | undefined);
}
/**
 * Error thrown when a node parameter is invalid or missing
 */
export declare class NodeParameterError extends BaseError {
    readonly nodeId: string;
    readonly parameterName: string;
    readonly expectedType?: string | undefined;
    readonly actualValue?: any | undefined;
    readonly nodeType?: string | undefined;
    constructor(nodeId: string, parameterName: string, expectedType?: string | undefined, actualValue?: any | undefined, nodeType?: string | undefined);
}
/**
 * Error thrown when workflow execution fails at the overall level
 */
export declare class WorkflowExecutionError extends BaseError {
    readonly executionId: string;
    readonly workflowId: string;
    readonly phase: 'validation' | 'execution' | 'cleanup';
    readonly context?: Record<string, any> | undefined;
    readonly failedNodeId?: string | undefined;
    constructor(message: string, executionId: string, workflowId: string, phase: 'validation' | 'execution' | 'cleanup', context?: Record<string, any> | undefined, failedNodeId?: string | undefined);
}
/**
 * Error that can be retried with specific retry configuration
 */
export declare class RetryableError extends BaseError {
    readonly retryAfter?: number | undefined;
    readonly maxRetries?: number | undefined;
    readonly retryCount?: number | undefined;
    readonly context?: Record<string, any> | undefined;
    constructor(message: string, retryAfter?: number | undefined, maxRetries?: number | undefined, retryCount?: number | undefined, context?: Record<string, any> | undefined);
    get shouldRetry(): boolean;
}
/**
 * Error thrown when data validation fails
 */
export declare class DataValidationError extends BaseError {
    readonly nodeId: string;
    readonly dataIndex?: number | undefined;
    readonly fieldPath?: string | undefined;
    readonly expectedSchema?: any | undefined;
    readonly actualData?: any | undefined;
    constructor(message: string, nodeId: string, dataIndex?: number | undefined, fieldPath?: string | undefined, expectedSchema?: any | undefined, actualData?: any | undefined);
}
/**
 * Error thrown when credentials are missing or invalid
 */
export declare class CredentialError extends BaseError {
    readonly credentialType: string;
    readonly nodeId?: string | undefined;
    readonly context?: Record<string, any> | undefined;
    constructor(message: string, credentialType: string, nodeId?: string | undefined, context?: Record<string, any> | undefined);
}
/**
 * Error thrown when external service is unavailable
 */
export declare class ServiceUnavailableError extends BaseError {
    readonly service: string;
    readonly statusCode: number;
    readonly responseBody?: string | undefined;
    readonly retryAfter?: number | undefined;
    constructor(service: string, statusCode?: number, responseBody?: string | undefined, retryAfter?: number | undefined);
    get shouldRetry(): boolean;
}
/**
 * Error thrown when rate limiting occurs
 */
export declare class RateLimitError extends BaseError {
    readonly service: string;
    readonly rateLimitReset?: number | undefined;
    readonly remainingRequests?: number | undefined;
    readonly retryAfter: number;
    constructor(service: string, rateLimitReset?: number | undefined, remainingRequests?: number | undefined);
    get shouldRetry(): boolean;
}
/**
 * Error thrown when node dependency is missing
 */
export declare class NodeDependencyError extends BaseError {
    readonly nodeId: string;
    readonly dependency: string;
    readonly nodeType?: string | undefined;
    constructor(nodeId: string, dependency: string, nodeType?: string | undefined);
}
/**
 * Error thrown when expression evaluation fails
 */
export declare class ExpressionError extends BaseError {
    readonly nodeId: string;
    readonly expression: string;
    readonly originalError: Error;
    readonly nodeType?: string | undefined;
    constructor(nodeId: string, expression: string, originalError: Error, nodeType?: string | undefined);
}
/**
 * Error thrown when workflow has circular dependencies
 */
export declare class CircularDependencyError extends BaseError {
    readonly workflowId: string;
    readonly executionId: string;
    readonly cycle: string[];
    constructor(workflowId: string, executionId: string, cycle: string[]);
}
/**
 * Error thrown when execution is terminated by user
 */
export declare class ExecutionTerminatedError extends BaseError {
    readonly executionId: string;
    readonly workflowId: string;
    readonly reason: string;
    constructor(executionId: string, workflowId: string, reason?: string);
}
//# sourceMappingURL=execution-errors.d.ts.map