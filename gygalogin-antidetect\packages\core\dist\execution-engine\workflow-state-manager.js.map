{"version": 3, "file": "workflow-state-manager.js", "sourceRoot": "", "sources": ["../../src/execution-engine/workflow-state-manager.ts"], "names": [], "mappings": ";AAAA,uDAAuD;;;AAIvD,IAAY,mBAMX;AAND,WAAY,mBAAmB;IAC7B,0CAAmB,CAAA;IACnB,0CAAmB,CAAA;IACnB,8CAAuB,CAAA;IACvB,wCAAiB,CAAA;IACjB,0CAAmB,CAAA;AACrB,CAAC,EANW,mBAAmB,mCAAnB,mBAAmB,QAM9B;AAeD,MAAa,oBAAoB;IAM/B,YAAoB,UAAkB,EAAU,WAAmB;QAA/C,eAAU,GAAV,UAAU,CAAQ;QAAU,gBAAW,GAAX,WAAW,CAAQ;QAL3D,eAAU,GAA4B,IAAI,GAAG,EAAE,CAAC;QAChD,iBAAY,GAAmC,IAAI,GAAG,EAAE,CAAC;IAIK,CAAC;IAEvE,wBAAwB;IACjB,aAAa,CAAC,MAAc,EAAE,MAA2B;QAC9D,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,mBAAmB,CAAC,OAAO,EAAE,CAAC;QAE5F,IAAI,MAAM,KAAK,mBAAmB,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YACtE,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QACtC,CAAC;QAED,IAAI,MAAM,KAAK,mBAAmB,CAAC,SAAS,IAAI,MAAM,KAAK,mBAAmB,CAAC,MAAM,EAAE,CAAC;YACtF,YAAY,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QACpC,CAAC;QAED,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;QAC7B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAC5C,CAAC;IAEM,aAAa,CAAC,MAAc;;QACjC,OAAO,CAAA,MAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,0CAAE,MAAM,KAAI,mBAAmB,CAAC,OAAO,CAAC;IAC5E,CAAC;IAEM,YAAY,CAAC,MAAc;QAChC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAEM,aAAa,CAAC,MAAc,EAAE,MAA4B;QAC/D,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,mBAAmB,CAAC,OAAO,EAAE,CAAC;QAC5F,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;QAC7B,YAAY,CAAC,MAAM,GAAG,mBAAmB,CAAC,SAAS,CAAC;QACpD,YAAY,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAC5C,CAAC;IAEM,aAAa,CAAC,MAAc;;QACjC,OAAO,MAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,0CAAE,MAAM,CAAC;IAC7C,CAAC;IAEM,YAAY,CAAC,MAAc,EAAE,KAAY;QAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,mBAAmB,CAAC,OAAO,EAAE,CAAC;QAC5F,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;QAC3B,YAAY,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC;QACjD,YAAY,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAC5C,CAAC;IAED,mDAAmD;IAC5C,qBAAqB,CAAC,MAAc,EAAE,cAAwB;QACnE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE;YAC5B,QAAQ,EAAE,EAAE;YACZ,cAAc;SACf,CAAC,CAAC;IACL,CAAC;IAEM,YAAY,CAAC,MAAc,EAAE,SAAiB,EAAE,IAA0B;QAC/E,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;QAEvC,4CAA4C;QAC5C,MAAM,iBAAiB,GAAG,WAAW,CAAC,cAAc,CAAC,KAAK,CACxD,SAAS,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,SAAS,CAC3D,CAAC;QAEF,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAEM,oBAAoB,CAAC,MAAc;QACxC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,8BAA8B;QAC9B,OAAO,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;IACpD,CAAC;IAEM,iBAAiB,CAAC,MAAc;QACrC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAEM,aAAa,CAAC,MAAc;QACjC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;IAED,qBAAqB;IACd,cAAc;QACnB,IAAI,CAAC,kBAAkB,GAAG,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC;IAEM,YAAY;QACjB,IAAI,CAAC,gBAAgB,GAAG,IAAI,IAAI,EAAE,CAAC;IACrC,CAAC;IAEM,oBAAoB;QACzB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,IAAI,EAAE,CAAC;QACpD,OAAO,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;IAC/D,CAAC;IAED,gBAAgB;IACT,iBAAiB;QACtB,MAAM,cAAc,GAAa,EAAE,CAAC;QACpC,KAAK,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC9C,IAAI,KAAK,CAAC,MAAM,KAAK,mBAAmB,CAAC,SAAS,EAAE,CAAC;gBACnD,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;IAEM,cAAc;QACnB,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,KAAK,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC9C,IAAI,KAAK,CAAC,MAAM,KAAK,mBAAmB,CAAC,MAAM,EAAE,CAAC;gBAChD,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAEM,eAAe;QACpB,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,KAAK,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC9C,IAAI,KAAK,CAAC,MAAM,KAAK,mBAAmB,CAAC,OAAO,EAAE,CAAC;gBACjD,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAEM,gBAAgB;QACrB,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IAEM,mBAAmB;QACxB,kFAAkF;QAClF,KAAK,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC9C,IAAI,KAAK,CAAC,MAAM,KAAK,mBAAmB,CAAC,OAAO;gBAC5C,CAAC,KAAK,CAAC,MAAM,KAAK,mBAAmB,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;gBAClF,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED,wBAAwB;IACjB,mBAAmB;QACxB,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,kBAAkB;YAClC,OAAO,EAAE,IAAI,CAAC,gBAAgB;YAC9B,QAAQ,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACrC,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,MAAM;YAC/C,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM;YACzC,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM;YAC3C,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;YAChC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE;YAC3B,UAAU,EAAE,IAAI,CAAC,mBAAmB,EAAE;SACvC,CAAC;IACJ,CAAC;CACF;AAjLD,oDAiLC"}