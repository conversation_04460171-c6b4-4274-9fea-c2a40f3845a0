import { DataSource, FindManyOptions, Repository } from 'typeorm';
import { ProfileEntity } from '../entities/profile.entity';
export interface ProfileSearchCriteria {
    name?: string;
    tags?: string[];
    isActive?: boolean;
    createdAfter?: Date;
    createdBefore?: Date;
    hasProxy?: boolean;
}
export declare class ProfileRepository extends Repository<ProfileEntity> {
    constructor(dataSource: DataSource);
    /**
     * Find entity by ID - added for compatibility with core package
     */
    findById(id: string): Promise<ProfileEntity | null>;
    /**
     * Create a new profile entity and return it
     */
    createProfile(profileData: Partial<ProfileEntity>): Promise<ProfileEntity>;
    /**
     * Update profile and return the updated entity
     */
    updateProfile(id: string, updateData: Partial<ProfileEntity>): Promise<ProfileEntity | null>;
    /**
     * Delete profile by id and return success status
     */
    deleteProfile(id: string): Promise<boolean>;
    /**
     * Find profiles by name (case-insensitive partial match)
     */
    findByName(name: string): Promise<ProfileEntity[]>;
    /**
     * Search profiles by name (alias for findByName for test compatibility)
     */
    searchByName(name: string): Promise<ProfileEntity[]>;
    /**
     * Find active profiles
     */
    findActive(): Promise<ProfileEntity[]>;
    /**
     * Find profiles by tags
     */
    findByTags(tags: string[]): Promise<ProfileEntity[]>;
    /**
     * Search profiles with multiple criteria
     */
    search(criteria: ProfileSearchCriteria, options?: FindManyOptions<ProfileEntity>): Promise<ProfileEntity[]>;
    /**
     * Find a single profile with its proxy data
     */
    findWithProxyById(id: string): Promise<ProfileEntity | null>;
    /**
     * Find all profiles with their proxy data
     */
    findWithProxy(): Promise<ProfileEntity[]>;
    /**
     * Find a single profile with its sessions
     */
    findWithSessionsById(id: string): Promise<ProfileEntity | null>;
    /**
     * Find all profiles with their sessions
     */
    findWithSessions(): Promise<ProfileEntity[]>;
    /**
     * Update last used timestamp
     */
    updateLastUsed(id: string): Promise<void>;
    /**
     * Get profiles usage statistics
     */
    getUsageStats(): Promise<{
        total: number;
        active: number;
        inactive: number;
        withProxy: number;
        withoutProxy: number;
        recentlyUsed: number;
    }>;
}
//# sourceMappingURL=profile.repository.d.ts.map