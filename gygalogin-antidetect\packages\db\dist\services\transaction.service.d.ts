import { QueryRunner } from 'typeorm';
import { Logger } from '@gygalogin/shared';
import { DatabaseService } from './database.service';
export declare class TransactionService {
    private readonly databaseService;
    private readonly logger;
    constructor(databaseService: DatabaseService, logger: Logger);
    runInTransaction<T>(operation: (queryRunner: QueryRunner) => Promise<T>): Promise<T>;
    withTransaction<T>(operation: (manager: any) => Promise<T>): Promise<T>;
}
//# sourceMappingURL=transaction.service.d.ts.map