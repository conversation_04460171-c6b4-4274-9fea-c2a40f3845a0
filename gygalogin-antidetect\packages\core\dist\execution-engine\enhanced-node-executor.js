"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedNodeExecutor = void 0;
const p_cancelable_1 = require("p-cancelable");
const workflow_node_context_1 = require("./node-execution-context/workflow-node-context");
const node_execution_functions_1 = require("../node-execution-functions");
const workflow_state_manager_1 = require("./workflow-state-manager");
const data_manager_1 = require("./data-manager");
const errors_1 = require("../errors");
const error_recovery_1 = require("./error-recovery");
/**
 * Enhanced Node Executor with advanced error handling, recovery, and monitoring
 */
class EnhancedNodeExecutor {
    constructor(workflow, stateManager, options = {}, abortSignal) {
        this.workflow = workflow;
        this.stateManager = stateManager;
        this.options = options;
        this.abortSignal = abortSignal;
        this.initializeErrorHandling();
    }
    /**
     * Execute a node with advanced error handling, recovery, and monitoring
     */
    executeNode(node_1, inputData_1, context_1) {
        return __awaiter(this, arguments, void 0, function* (node, inputData, context, nodeOptions = {}) {
            var _a, _b;
            const mergedOptions = Object.assign(Object.assign({}, this.options), nodeOptions);
            const startTime = Date.now();
            // Check if execution was aborted
            if ((_a = this.abortSignal) === null || _a === void 0 ? void 0 : _a.aborted) {
                throw new p_cancelable_1.CancelError('Node execution was canceled.');
            }
            // Set node status to running
            this.stateManager.setNodeStatus(node.id, workflow_state_manager_1.NodeExecutionStatus.RUNNING);
            // Get retry settings from node configuration or options
            const nodeRetrySettings = node.retryOnFail;
            const retrySettings = mergedOptions.retrySettings || nodeRetrySettings;
            const maxRetries = (retrySettings === null || retrySettings === void 0 ? void 0 : retrySettings.count) || mergedOptions.retries || 0;
            let lastError = null;
            const previousErrors = [];
            // Enhanced retry logic with error recovery
            for (let attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    // Check for abort between retries
                    if ((_b = this.abortSignal) === null || _b === void 0 ? void 0 : _b.aborted) {
                        throw new p_cancelable_1.CancelError('Node execution was canceled.');
                    }
                    const result = yield this.attemptNodeExecution(node, inputData, context, mergedOptions, attempt);
                    // Mark as completed and store output
                    this.stateManager.setNodeOutput(node.id, result);
                    // Record successful execution if monitoring is enabled
                    if (this.errorMonitor && attempt > 0) {
                        // This was a successful recovery
                        this.errorMonitor.recordError(new Error('Recovered from previous errors'), node.id, node.type, context.getWorkflowId() || 'unknown', context.getExecutionId() || 'unknown', { success: true, attempt, previousErrors }, { recovery: 'success', elapsedTime: Date.now() - startTime });
                    }
                    return result;
                }
                catch (error) {
                    lastError = error;
                    previousErrors.push(lastError);
                    // If it's a cancellation, don't retry
                    if (error instanceof p_cancelable_1.CancelError) {
                        this.stateManager.setNodeError(node.id, error);
                        throw error;
                    }
                    // Record error for monitoring
                    if (this.errorMonitor) {
                        this.errorMonitor.recordError(lastError, node.id, node.type, context.getWorkflowId() || 'unknown', context.getExecutionId() || 'unknown', undefined, { attempt, elapsedTime: Date.now() - startTime });
                    }
                    // If this is the last attempt, try error recovery or handle error
                    if (attempt === maxRetries) {
                        if (mergedOptions.enableErrorRecovery && this.errorRecoveryManager) {
                            try {
                                const recoveryResult = yield this.errorRecoveryManager.attemptRecovery(lastError, node, {
                                    workflowId: context.getWorkflowId(),
                                    executionId: context.getExecutionId(),
                                    attempt,
                                    maxAttempts: maxRetries,
                                    startTime: new Date(startTime),
                                    elapsedTime: Date.now() - startTime,
                                    inputData,
                                    previousErrors
                                });
                                if (recoveryResult.action === 'retry') {
                                    // Extend retry count for recovery attempt
                                    const recoveredInputData = recoveryResult.modifiedInputData || inputData;
                                    const recoveredOptions = recoveryResult.modifiedParameters
                                        ? Object.assign(Object.assign({}, mergedOptions), recoveryResult.modifiedParameters) : mergedOptions;
                                    if (recoveryResult.delay) {
                                        yield this.delay(recoveryResult.delay);
                                    }
                                    // Try one more time with recovery modifications
                                    try {
                                        const result = yield this.attemptNodeExecution(node, recoveredInputData, context, recoveredOptions, attempt + 1);
                                        this.stateManager.setNodeOutput(node.id, result);
                                        return result;
                                    }
                                    catch (recoveryError) {
                                        // Recovery failed, proceed to error handling
                                        return this.handleNodeError(node, recoveryError, mergedOptions);
                                    }
                                }
                                else if (recoveryResult.action === 'skip') {
                                    const skipOutput = recoveryResult.fallbackOutput || [{
                                            json: {
                                                skipped: true,
                                                reason: recoveryResult.reason,
                                                originalError: lastError.message
                                            },
                                            pairedItem: { item: 0 }
                                        }];
                                    this.stateManager.setNodeOutput(node.id, skipOutput);
                                    return skipOutput;
                                }
                                else if (recoveryResult.action === 'fallback' && recoveryResult.fallbackOutput) {
                                    this.stateManager.setNodeOutput(node.id, recoveryResult.fallbackOutput);
                                    return recoveryResult.fallbackOutput;
                                }
                            }
                            catch (recoveryError) {
                                // Recovery itself failed, continue to normal error handling
                                console.warn('Error recovery failed:', recoveryError);
                            }
                        }
                        return this.handleNodeError(node, lastError, mergedOptions);
                    }
                    // Wait before retry with configurable backoff
                    if (attempt < maxRetries) {
                        const delay = this.calculateRetryDelay(attempt, retrySettings);
                        yield this.delay(delay);
                    }
                }
            }
            // This should never be reached, but handle it just in case
            if (lastError) {
                return this.handleNodeError(node, lastError, mergedOptions);
            }
            throw new errors_1.NodeExecutionError('Unexpected error in node execution retry logic', node.id, node.type);
        });
    }
    /**
     * Get error recovery statistics
     */
    getRecoveryStats() {
        var _a;
        return (_a = this.errorRecoveryManager) === null || _a === void 0 ? void 0 : _a.getStats();
    }
    /**
     * Get error monitoring statistics
     */
    getMonitoringStats() {
        var _a;
        return (_a = this.errorMonitor) === null || _a === void 0 ? void 0 : _a.getStats();
    }
    /**
     * Get recent error alerts
     */
    getRecentAlerts() {
        var _a;
        return (_a = this.errorMonitor) === null || _a === void 0 ? void 0 : _a.getAlerts(10);
    }
    /**
     * Generate error report
     */
    generateErrorReport(timeWindow) {
        var _a;
        return (_a = this.errorMonitor) === null || _a === void 0 ? void 0 : _a.generateReport(timeWindow);
    }
    /**
     * Cleanup resources
     */
    destroy() {
        var _a;
        (_a = this.errorMonitor) === null || _a === void 0 ? void 0 : _a.destroy();
    }
    initializeErrorHandling() {
        if (this.options.enableErrorRecovery) {
            this.errorRecoveryManager = new error_recovery_1.ErrorRecoveryManager(this.options.errorRecoveryConfig || {});
        }
        if (this.options.enableErrorMonitoring) {
            const defaultMonitorConfig = {
                maxOccurrenceHistory: 1000,
                cleanupInterval: 300000, // 5 minutes
                defaultTimeWindow: 3600000, // 1 hour
                enableRealTimeMonitoring: true,
                alertHandlers: [],
                customPatterns: []
            };
            this.errorMonitor = new error_recovery_1.ErrorMonitor(Object.assign(Object.assign({}, defaultMonitorConfig), this.options.errorMonitorConfig));
            // Connect error recovery events to monitoring
            if (this.errorRecoveryManager) {
                this.errorRecoveryManager.onRecoveryEvent(event => { var _a; return (_a = this.errorMonitor) === null || _a === void 0 ? void 0 : _a.handleRecoveryEvent(event); });
            }
        }
    }
    attemptNodeExecution(node, inputData, context, options, attempt) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            // Enhanced parameter validation
            this.validateNodeParameters(node);
            // Get the node type implementation
            const nodeType = (_a = this.workflow.nodeTypes) === null || _a === void 0 ? void 0 : _a.getByNameAndVersion(node.type, node.typeVersion || 1);
            if (!nodeType) {
                throw new errors_1.NodeExecutionError(`Node type "${node.type}" not found`, node.id, node.type, { typeVersion: node.typeVersion }, attempt);
            }
            // Create execution context for this node
            const nodeContext = new workflow_node_context_1.WorkflowNodeContext(this.workflow, node, context.getAll());
            const executeFunctions = new node_execution_functions_1.NodeExecuteFunctions(nodeContext, node, inputData);
            // Enhanced timeout handling
            const timeout = options.timeout || node.parameters.timeout;
            let executionPromise;
            if (!nodeType.execute) {
                throw new errors_1.NodeExecutionError(`Node type "${node.type}" does not have an execute method`, node.id, node.type, { typeVersion: node.typeVersion });
            }
            executionPromise = nodeType.execute.call(executeFunctions);
            // Apply timeout if specified with enhanced error
            if (timeout && timeout > 0) {
                executionPromise = this.executeWithEnhancedTimeout(executionPromise, timeout, node, attempt);
            }
            const result = yield executionPromise;
            // Validate output data
            this.validateOutputData(result, node);
            // Normalize result to always be INodeExecutionData[]
            if (Array.isArray(result) && Array.isArray(result[0])) {
                return result.flat();
            }
            return result;
        });
    }
    validateNodeParameters(node) {
        // Basic parameter validation - can be extended
        if (!node.parameters) {
            throw new errors_1.NodeParameterError(node.id, 'parameters', 'object', node.parameters, node.type);
        }
        // Add more specific validation based on node type
        // This is a placeholder for future enhancement
    }
    validateOutputData(data, node) {
        if (!Array.isArray(data) && !Array.isArray(data === null || data === void 0 ? void 0 : data[0])) {
            throw new errors_1.NodeExecutionError('Node execution must return an array or array of arrays', node.id, node.type, { returnType: typeof data });
        }
    }
    handleNodeError(node, error, options) {
        var _a, _b;
        // Enhanced error wrapping
        const nodeError = error instanceof errors_1.NodeExecutionError
            ? error
            : new errors_1.NodeExecutionError(error.message, node.id, node.type, { originalError: error.constructor.name, stack: error.stack });
        // Mark node as failed
        this.stateManager.setNodeError(node.id, nodeError);
        // Check if we should continue on fail
        const continueOnFail = (_b = (_a = options.continueOnFail) !== null && _a !== void 0 ? _a : node.continueOnFail) !== null && _b !== void 0 ? _b : false;
        if (continueOnFail) {
            // Return enhanced error data
            const errorData = [{
                    json: {
                        error: nodeError.message,
                        errorCode: nodeError.code,
                        errorType: nodeError.constructor.name,
                        nodeId: node.id,
                        nodeName: node.name,
                        nodeType: node.type,
                        stack: nodeError.stack,
                        context: nodeError.context,
                        timestamp: new Date().toISOString()
                    },
                    pairedItem: { item: 0 },
                }];
            // Store error output in state manager
            this.stateManager.setNodeOutput(node.id, errorData);
            return errorData;
        }
        // Re-throw the enhanced error if not continuing on fail
        throw nodeError;
    }
    executeWithEnhancedTimeout(promise, timeoutMs, node, attempt) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new errors_1.NodeTimeoutError(node.id, timeoutMs, node.type, { attempt }));
            }, timeoutMs);
            promise
                .then(result => {
                clearTimeout(timeoutId);
                resolve(result);
            })
                .catch(error => {
                clearTimeout(timeoutId);
                reject(error);
            });
        });
    }
    calculateRetryDelay(attempt, retrySettings) {
        const baseDelay = (retrySettings === null || retrySettings === void 0 ? void 0 : retrySettings.delay) || 1000; // Default 1 second
        const backoffType = (retrySettings === null || retrySettings === void 0 ? void 0 : retrySettings.backoff) || 'exponential';
        switch (backoffType) {
            case 'linear':
                return baseDelay * (attempt + 1);
            case 'exponential':
            default:
                // Cap at 30 seconds for exponential backoff
                return Math.min(baseDelay * Math.pow(2, attempt), 30000);
        }
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    // Static method to apply paired items using DataManager (enhanced version)
    static applyPairedItem(inputData, outputData) {
        return data_manager_1.DataManager.applyPairedItem(inputData, outputData);
    }
}
exports.EnhancedNodeExecutor = EnhancedNodeExecutor;
//# sourceMappingURL=enhanced-node-executor.js.map