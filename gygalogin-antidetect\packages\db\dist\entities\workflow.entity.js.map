{"version": 3, "file": "workflow.entity.js", "sourceRoot": "", "sources": ["../../src/entities/workflow.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAAgH;AAChH,yDAAqD;AAG9C,IAAM,cAAc,GAApB,MAAM,cAAc;CAuE1B,CAAA;AAvEY,wCAAc;AAEzB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;0CACpB;AAGX;IADC,IAAA,gBAAM,GAAE;;4CACI;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDACN;AAIrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8BAClB,KAAK;6CAYT;AAIH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8BACZ,KAAK;mDAKf;AAGH;IADC,IAAA,gBAAM,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;8CACX;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDAMvC;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDACR;AAGjC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CAKrC;AAGJ;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACjC;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDACR;AAGnB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;iDAAC;AAGhB;IADC,IAAA,0BAAgB,GAAE;8BACR,IAAI;iDAAC;AAIhB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kCAAe,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC;;kDACpC;yBAtEnB,cAAc;IAD1B,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;GACjB,cAAc,CAuE1B"}