"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionService = void 0;
const decorators_1 = require("@gygalogin/decorators");
const shared_1 = require("@gygalogin/shared");
const database_service_1 = require("./database.service");
let TransactionService = class TransactionService {
    constructor(databaseService, logger) {
        this.databaseService = databaseService;
        this.logger = logger;
    }
    async runInTransaction(operation) {
        const dataSource = this.databaseService.getDataSource();
        const queryRunner = dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const result = await operation(queryRunner);
            await queryRunner.commitTransaction();
            this.logger.debug('✅ Transaction committed successfully');
            return result;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('❌ Transaction rolled back:', error);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async withTransaction(operation) {
        const dataSource = this.databaseService.getDataSource();
        return dataSource.transaction(async (manager) => {
            return await operation(manager);
        });
    }
};
exports.TransactionService = TransactionService;
exports.TransactionService = TransactionService = __decorate([
    (0, decorators_1.Service)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService,
        shared_1.Logger])
], TransactionService);
//# sourceMappingURL=transaction.service.js.map