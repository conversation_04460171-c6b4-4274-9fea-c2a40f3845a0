"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowRepository = void 0;
const decorators_1 = require("@gygalogin/decorators");
const typeorm_1 = require("typeorm");
const workflow_entity_1 = require("../entities/workflow.entity");
let WorkflowRepository = class WorkflowRepository extends typeorm_1.Repository {
    constructor(dataSource) {
        super(workflow_entity_1.WorkflowEntity, dataSource.manager);
    }
    /**
     * Find entity by ID - added for compatibility with core package
     */
    async findById(id) {
        return this.findOne({ where: { id } });
    }
    /**
     * Create a new workflow entity and return it
     */
    async createWorkflow(workflowData) {
        const workflow = this.create(workflowData);
        return this.save(workflow);
    }
    /**
     * Update workflow and return the updated entity
     */
    async updateWorkflow(id, updateData) {
        await this.update(id, updateData);
        return this.findOneBy({ id });
    }
    /**
     * Delete workflow by id and return success status
     */
    async deleteWorkflow(id) {
        const result = await this.delete(id);
        return (result.affected ?? 0) > 0;
    }
    /**
     * Find workflows by name (case-insensitive partial match)
     */
    async findByName(name) {
        return this.createQueryBuilder('workflow')
            .where('LOWER(workflow.name) LIKE LOWER(:name)', { name: `%${name}%` })
            .getMany();
    }
    /**
     * Find active workflows
     */
    async findActive() {
        return this.findBy({ active: true });
    }
    /**
     * Find inactive workflows
     */
    async findInactive() {
        return this.findBy({ active: false });
    }
    /**
     * Search workflows with multiple criteria
     */
    async search(criteria, options) {
        const queryBuilder = this.createQueryBuilder('workflow');
        if (criteria.name) {
            queryBuilder.andWhere('LOWER(workflow.name) LIKE LOWER(:name)', {
                name: `%${criteria.name}%`,
            });
        }
        if (criteria.active !== undefined) {
            queryBuilder.andWhere('workflow.active = :active', { active: criteria.active });
        }
        if (criteria.createdAfter) {
            queryBuilder.andWhere('workflow.createdAt >= :createdAfter', {
                createdAfter: criteria.createdAfter,
            });
        }
        if (criteria.createdBefore) {
            queryBuilder.andWhere('workflow.createdAt <= :createdBefore', {
                createdBefore: criteria.createdBefore,
            });
        }
        if (options?.take) {
            queryBuilder.take(options.take);
        }
        if (options?.skip) {
            queryBuilder.skip(options.skip);
        }
        if (options?.order) {
            Object.entries(options.order).forEach(([key, direction]) => {
                queryBuilder.addOrderBy(`workflow.${key}`, direction);
            });
        }
        return queryBuilder.getMany();
    }
    /**
     * Activate a workflow
     */
    async activate(id) {
        await this.update(id, { active: true });
        return this.findOneBy({ id });
    }
    /**
     * Deactivate a workflow
     */
    async deactivate(id) {
        await this.update(id, { active: false });
        return this.findOneBy({ id });
    }
    /**
     * Find workflow with its executions
     */
    async findWithExecutions(id) {
        return this.createQueryBuilder('workflow')
            .leftJoinAndSelect('workflow.executions', 'execution')
            .where('workflow.id = :id', { id })
            .orderBy('execution.startedAt', 'DESC')
            .getOne();
    }
    /**
     * Get workflow statistics
     */
    async getStats() {
        const total = await this.count();
        const active = await this.countBy({ active: true });
        const inactive = total - active;
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        const recentlyCreated = await this.createQueryBuilder('workflow')
            .where('workflow.createdAt >= :sevenDaysAgo', { sevenDaysAgo })
            .getCount();
        const totalExecutions = await this.createQueryBuilder('workflow')
            .leftJoin('workflow.executions', 'execution')
            .select('COUNT(execution.id)', 'count')
            .getRawOne()
            .then(result => parseInt(result.count) || 0);
        return {
            total,
            active,
            inactive,
            recentlyCreated,
            totalExecutions,
        };
    }
    /**
     * Duplicate a workflow
     */
    async duplicate(id, newName) {
        const original = await this.findOneBy({ id });
        if (!original) {
            return null;
        }
        const duplicatedWorkflow = {
            name: newName || `${original.name} (Copy)`,
            nodes: original.nodes,
            connections: original.connections,
            active: false, // Duplicated workflows start as inactive
            settings: original.settings,
            staticData: original.staticData,
            tags: original.tags,
            versionId: original.versionId,
        };
        const created = this.create(duplicatedWorkflow);
        return this.save(created);
    }
};
exports.WorkflowRepository = WorkflowRepository;
exports.WorkflowRepository = WorkflowRepository = __decorate([
    (0, decorators_1.Service)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], WorkflowRepository);
//# sourceMappingURL=workflow.repository.js.map