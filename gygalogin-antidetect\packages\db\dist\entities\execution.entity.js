"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecutionEntity = exports.ExecutionMode = exports.ExecutionStatus = void 0;
const typeorm_1 = require("typeorm");
const workflow_entity_1 = require("./workflow.entity");
var ExecutionStatus;
(function (ExecutionStatus) {
    ExecutionStatus["WAITING"] = "waiting";
    ExecutionStatus["RUNNING"] = "running";
    ExecutionStatus["SUCCESS"] = "success";
    ExecutionStatus["ERROR"] = "error";
    ExecutionStatus["CANCELED"] = "canceled";
    ExecutionStatus["CRASHED"] = "crashed";
})(ExecutionStatus || (exports.ExecutionStatus = ExecutionStatus = {}));
var ExecutionMode;
(function (ExecutionMode) {
    ExecutionMode["MANUAL"] = "manual";
    ExecutionMode["TRIGGER"] = "trigger";
    ExecutionMode["WEBHOOK"] = "webhook";
    ExecutionMode["POLLING"] = "polling";
    ExecutionMode["CLI"] = "cli";
    ExecutionMode["ERROR_TRIGGER"] = "error_trigger";
    ExecutionMode["INTERNAL"] = "internal";
})(ExecutionMode || (exports.ExecutionMode = ExecutionMode = {}));
let ExecutionEntity = class ExecutionEntity {
};
exports.ExecutionEntity = ExecutionEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ExecutionEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], ExecutionEntity.prototype, "workflowId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => workflow_entity_1.WorkflowEntity, workflow => workflow.executions, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'workflowId' }),
    __metadata("design:type", workflow_entity_1.WorkflowEntity)
], ExecutionEntity.prototype, "workflow", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        enum: Object.values(ExecutionStatus),
        default: ExecutionStatus.WAITING
    }),
    __metadata("design:type", String)
], ExecutionEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        enum: Object.values(ExecutionMode),
        default: ExecutionMode.MANUAL
    }),
    __metadata("design:type", String)
], ExecutionEntity.prototype, "mode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], ExecutionEntity.prototype, "startedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], ExecutionEntity.prototype, "stoppedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], ExecutionEntity.prototype, "finishedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ExecutionEntity.prototype, "data", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ExecutionEntity.prototype, "workflowData", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ExecutionEntity.prototype, "retryOf", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], ExecutionEntity.prototype, "retrySuccessId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ExecutionEntity.prototype, "error", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ExecutionEntity.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ExecutionEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ExecutionEntity.prototype, "updatedAt", void 0);
exports.ExecutionEntity = ExecutionEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'executions' })
], ExecutionEntity);
//# sourceMappingURL=execution.entity.js.map