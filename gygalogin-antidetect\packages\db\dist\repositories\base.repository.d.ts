import { Repository, FindOptionsWhere, FindManyOptions, DeepPartial, ObjectLiteral } from 'typeorm';
export declare abstract class BaseRepository<T extends ObjectLiteral> {
    protected repository: Repository<T>;
    constructor(repository: Repository<T>);
    findById(id: string): Promise<T | null>;
    findAll(options?: FindManyOptions<T>): Promise<T[]>;
    create(data: DeepPartial<T>): Promise<T>;
    update(id: string, data: DeepPartial<T>): Promise<T | null>;
    delete(id: string): Promise<boolean>;
    count(where?: FindOptionsWhere<T>): Promise<number>;
    exists(where: FindOptionsWhere<T>): Promise<boolean>;
    findBy(where: FindOptionsWhere<T>, options?: FindManyOptions<T>): Promise<T[]>;
    findOneBy(where: FindOptionsWhere<T>): Promise<T | null>;
}
//# sourceMappingURL=base.repository.d.ts.map