{"version": 3, "file": "error-recovery-manager.js", "sourceRoot": "", "sources": ["../../../src/execution-engine/error-recovery/error-recovery-manager.ts"], "names": [], "mappings": ";AAAA,uDAAuD;;;;;;;;;;;;AAGvD,qDAW0B;AAwC1B;;GAEG;AACH,MAAa,oBAAoB;IAY/B,YAAoB,SAA8B,EAAE;QAAhC,WAAM,GAAN,MAAM,CAA0B;QAX5C,eAAU,GAA6B,EAAE,CAAC;QAC1C,UAAK,GAAuB;YAClC,aAAa,EAAE,CAAC;YAChB,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,cAAc,EAAE,EAAE;YAClB,iBAAiB,EAAE,CAAC;YACpB,mBAAmB,EAAE,CAAC;SACvB,CAAC;QACM,mBAAc,GAA4C,EAAE,CAAC;QAGnE,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,QAA6C;QAClE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,QAA6C;QAC9E,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACU,eAAe,CAC1B,KAAY,EACZ,IAAW,EACX,OAAsC;;YAEtC,MAAM,eAAe,GAAyB;gBAC5C,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,SAAS;gBAC3C,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,SAAS;gBAC7C,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,CAAC;gBAC7B,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC;gBACrC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE;gBAC1C,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC;gBACrC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,cAAc,EAAE,IAAI,CAAC,UAAU;gBAC/B,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,EAAE;gBAC5C,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;aACjC,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAE3B,8BAA8B;YAC9B,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAE,kBAAkB;gBACxB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,KAAK;aACN,CAAC,CAAC;YAEH,IAAI,CAAC;gBACH,gDAAgD;gBAChD,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;oBAC7F,MAAM,MAAM,GAAwB;wBAClC,MAAM,EAAE,MAAM;wBACd,MAAM,EAAE,0BAA0B,IAAI,CAAC,MAAM,CAAC,eAAe,cAAc;qBAC5E,CAAC;oBAEF,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;oBACpE,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAED,6CAA6C;gBAC7C,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,eAAe,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;oBAC5F,MAAM,MAAM,GAAwB;wBAClC,MAAM,EAAE,MAAM;wBACd,MAAM,EAAE,2BAA2B,IAAI,CAAC,MAAM,CAAC,gBAAgB,YAAY;qBAC5E,CAAC;oBAEF,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;oBACpE,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAED,0EAA0E;gBAC1E,MAAM,oBAAoB,GAAG,IAAI,CAAC,UAAU;qBACzC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;qBACzD,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;qBAC9D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;gBAE3C,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACtC,MAAM,MAAM,GAAwB;wBAClC,MAAM,EAAE,MAAM;wBACd,MAAM,EAAE,yCAAyC;qBAClD,CAAC;oBAEF,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;oBACpE,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAED,yDAAyD;gBACzD,KAAK,MAAM,QAAQ,IAAI,oBAAoB,EAAE,CAAC;oBAC5C,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;wBAE9D,uBAAuB;wBACvB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;wBAE/F,8BAA8B;wBAC9B,IAAI,CAAC,SAAS,CAAC;4BACb,IAAI,EAAE,kBAAkB;4BACxB,SAAS,EAAE,IAAI,IAAI,EAAE;4BACrB,MAAM,EAAE,IAAI,CAAC,EAAE;4BACf,KAAK;4BACL,QAAQ,EAAE,QAAQ,CAAC,IAAI;4BACvB,MAAM;yBACP,CAAC,CAAC;wBAEH,yEAAyE;wBACzE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC;wBAC9C,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;wBAE3E,OAAO,MAAM,CAAC;oBAChB,CAAC;oBAAC,OAAO,aAAa,EAAE,CAAC;wBACvB,sDAAsD;wBACtD,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;4BACpC,OAAO,CAAC,IAAI,CAAC,qBAAqB,QAAQ,CAAC,IAAI,UAAU,EAAE,aAAa,CAAC,CAAC;wBAC5E,CAAC;wBACD,SAAS;oBACX,CAAC;gBACH,CAAC;gBAED,wDAAwD;gBACxD,MAAM,MAAM,GAAwB;oBAClC,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,6DAA6D;iBACtE,CAAC;gBAEF,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;gBACpE,OAAO,MAAM,CAAC;YAEhB,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,MAAM,YAAY,GAAG,aAAa,YAAY,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;gBACpG,MAAM,MAAM,GAAwB;oBAClC,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,4BAA4B,YAAY,EAAE;iBACnD,CAAC;gBAEF,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;gBACpE,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;KAAA;IAED;;OAEG;IACI,WAAW,CAAC,QAAgC;QACjD,uDAAuD;QACvD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC;QACxE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE/B,mBAAmB;QACnB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,IAAY;QAChC,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,aAAa,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,aAAa;QAClB,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,yBAAY,IAAI,CAAC,KAAK,EAAG;IAC3B,CAAC;IAED;;OAEG;IACI,UAAU;QACf,IAAI,CAAC,KAAK,GAAG;YACX,aAAa,EAAE,CAAC;YAChB,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,cAAc,EAAE,EAAE;YAClB,iBAAiB,EAAE,CAAC;YACpB,mBAAmB,EAAE,CAAC;SACvB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,MAAoC;QACtD,IAAI,CAAC,MAAM,mCAAQ,IAAI,CAAC,MAAM,GAAK,MAAM,CAAE,CAAC;IAC9C,CAAC;IAEO,2BAA2B;QACjC,MAAM,iBAAiB,GAAG;YACxB,IAAI,0CAAyB,EAAE;YAC/B,IAAI,2CAA0B,EAAE;YAChC,IAAI,mDAAkC,EAAE;YACxC,IAAI,wCAAuB,EAAE;YAC7B,IAAI,+CAA8B,EAAE;YACpC,IAAI,6CAA4B,EAAE;YAClC,IAAI,yCAAwB,EAAE;SAC/B,CAAC;QAEF,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;IACpE,CAAC;IAEO,mBAAmB;QACzB,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,YAAoB;QAC5C,qDAAqD;QACrD,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9E,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC9D,CAAC;QAED,qDAAqD;QACrD,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAChE,CAAC;QAED,kCAAkC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,oBAAoB,CAC1B,MAA2B,EAC3B,KAAY,EACZ,MAAc,EACd,SAAiB,EACjB,YAAqB;QAErB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAE3C,oBAAoB;QACpB,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,WAAW,CAAC;QAC5C,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;QAEzF,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAChC,CAAC;QAED,wBAAwB;QACxB,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,iBAAiB;YAC7D,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM;YACN,KAAK;YACL,MAAM;YACN,WAAW;SACZ,CAAC,CAAC;QAEH,iBAAiB;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;YACpC,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,mBAAmB,MAAM,KAAK,MAAM,CAAC,MAAM,MAAM,MAAM,CAAC,MAAM,KAAK,WAAW,KAAK,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;IAEO,SAAS,CAAC,KAAyB;QACzC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACrC,IAAI,CAAC;gBACH,QAAQ,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAChE,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAxSD,oDAwSC"}