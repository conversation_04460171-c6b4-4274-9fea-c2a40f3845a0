import { ExecutionEntity } from './execution.entity';
export declare class WorkflowEntity {
    id: string;
    name: string;
    description?: string;
    nodes: Array<{
        id: string;
        name: string;
        type: string;
        typeVersion: number;
        position: [number, number];
        disabled?: boolean;
        notes?: string;
        parameters: Record<string, any>;
        credentials?: Record<string, any>;
        continueOnFail?: boolean;
        errorOutput?: string;
    }>;
    connections: Array<{
        sourceNodeId: string;
        sourceOutput: string | 'error';
        targetNodeId: string;
        targetInput: string;
    }>;
    active: boolean;
    settings?: {
        timezone?: string;
        hooks?: Record<string, any>;
        errorBehaviour?: 'stop' | 'continue';
        [key: string]: any;
    };
    staticData?: Record<string, any>;
    pinData?: Record<string, Array<{
        json: Record<string, any>;
        binary?: Record<string, any>;
        pairedItem?: {
            item: number;
        };
    }>>;
    tags?: string[];
    versionId?: string;
    createdAt: Date;
    updatedAt: Date;
    executions: ExecutionEntity[];
}
//# sourceMappingURL=workflow.entity.d.ts.map