"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockCredentialEncryption = exports.MockCredentialRepository = exports.CredentialService = void 0;
exports.getCredentialService = getCredentialService;
exports.setDefaultCredentialService = setDefaultCredentialService;
class CredentialService {
    constructor(options = {}) {
        var _a, _b;
        this.cache = new Map();
        this.repository = options.repository;
        this.encryption = options.encryption;
        this.cacheEnabled = (_a = options.cacheEnabled) !== null && _a !== void 0 ? _a : true;
        this.cacheTTL = (_b = options.cacheTTL) !== null && _b !== void 0 ? _b : 300000; // 5 minutes default
    }
    /**
     * Get credentials by name for the current execution context
     */
    getCredentials(name) {
        return __awaiter(this, void 0, void 0, function* () {
            // Check cache first
            if (this.cacheEnabled) {
                const cached = this.getCachedCredential(name);
                if (cached) {
                    return cached;
                }
            }
            if (!this.repository) {
                throw new Error('Credential repository not configured');
            }
            // Fetch from repository
            const credential = yield this.repository.findByName(name);
            if (!credential) {
                throw new Error(`Credentials with name "${name}" not found`);
            }
            // Decrypt if encryption service is available
            let decryptedData = credential.data;
            if (this.encryption) {
                try {
                    // Assuming the data is stored encrypted as a string in the database
                    decryptedData = yield this.encryption.decrypt(credential.data);
                }
                catch (error) {
                    throw new Error(`Failed to decrypt credentials "${name}": ${error.message}`);
                }
            }
            // Cache the decrypted data
            if (this.cacheEnabled) {
                this.setCachedCredential(name, decryptedData);
            }
            return decryptedData;
        });
    }
    /**
     * Get credentials by ID
     */
    getCredentialsById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.repository) {
                throw new Error('Credential repository not configured');
            }
            const credential = yield this.repository.findById(id);
            if (!credential) {
                throw new Error(`Credentials with ID "${id}" not found`);
            }
            let decryptedData = credential.data;
            if (this.encryption) {
                try {
                    decryptedData = yield this.encryption.decrypt(credential.data);
                }
                catch (error) {
                    throw new Error(`Failed to decrypt credentials with ID "${id}": ${error.message}`);
                }
            }
            return decryptedData;
        });
    }
    /**
     * Store new credentials
     */
    storeCredentials(name, type, data) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.repository) {
                throw new Error('Credential repository not configured');
            }
            // Encrypt data if encryption service is available
            let dataToStore = data;
            if (this.encryption) {
                try {
                    dataToStore = yield this.encryption.encrypt(data);
                }
                catch (error) {
                    throw new Error(`Failed to encrypt credentials: ${error.message}`);
                }
            }
            const credential = yield this.repository.create({
                name,
                type,
                data: dataToStore,
            });
            // Cache the original decrypted data
            if (this.cacheEnabled) {
                this.setCachedCredential(name, data);
            }
            return credential;
        });
    }
    /**
     * Update existing credentials
     */
    updateCredentials(id, updates) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.repository) {
                throw new Error('Credential repository not configured');
            }
            // Get the existing credential to know its name for cache clearing
            const existing = yield this.repository.findById(id);
            if (!existing) {
                throw new Error(`Credential with ID ${id} not found`);
            }
            const processedUpdates = Object.assign({}, updates);
            // Encrypt data if provided and encryption service is available
            if (updates.data && this.encryption) {
                try {
                    processedUpdates.data = (yield this.encryption.encrypt(updates.data));
                }
                catch (error) {
                    throw new Error(`Failed to encrypt credentials: ${error.message}`);
                }
            }
            const credential = yield this.repository.update(id, processedUpdates);
            // Clear cache for this credential (use existing name or updated name)
            if (this.cacheEnabled) {
                const nameToDelete = updates.name || existing.name;
                this.cache.delete(nameToDelete);
                // If name is being changed, also clear old name from cache
                if (updates.name && updates.name !== existing.name) {
                    this.cache.delete(existing.name);
                }
            }
            return credential;
        });
    }
    /**
     * Delete credentials
     */
    deleteCredentials(id, name) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.repository) {
                throw new Error('Credential repository not configured');
            }
            yield this.repository.delete(id);
            // Clear cache
            if (this.cacheEnabled && name) {
                this.cache.delete(name);
            }
        });
    }
    /**
     * Test credentials
     */
    testCredentials(request) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.repository) {
                throw new Error('Credential repository not configured');
            }
            return this.repository.testCredentials(request);
        });
    }
    /**
     * List credentials by type
     */
    getCredentialsByType(type) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.repository) {
                throw new Error('Credential repository not configured');
            }
            return this.repository.findByType(type);
        });
    }
    /**
     * Clear all cached credentials
     */
    clearCache() {
        this.cache.clear();
    }
    /**
     * Clear expired cache entries
     */
    cleanExpiredCache() {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (value.expires <= now) {
                this.cache.delete(key);
            }
        }
    }
    /**
     * Set credential repository
     */
    setRepository(repository) {
        this.repository = repository;
    }
    /**
     * Set encryption service
     */
    setEncryption(encryption) {
        this.encryption = encryption;
    }
    getCachedCredential(name) {
        const cached = this.cache.get(name);
        if (!cached) {
            return null;
        }
        if (cached.expires <= Date.now()) {
            this.cache.delete(name);
            return null;
        }
        return cached.data;
    }
    setCachedCredential(name, data) {
        const expires = Date.now() + this.cacheTTL;
        this.cache.set(name, { data, expires });
    }
}
exports.CredentialService = CredentialService;
// Default credential service instance (singleton pattern)
let defaultCredentialService = null;
function getCredentialService() {
    if (!defaultCredentialService) {
        defaultCredentialService = new CredentialService();
    }
    return defaultCredentialService;
}
function setDefaultCredentialService(service) {
    defaultCredentialService = service;
}
// Mock implementations for development/testing
class MockCredentialRepository {
    constructor() {
        this.credentials = new Map();
    }
    findById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.credentials.get(id) || null;
        });
    }
    findByName(name) {
        return __awaiter(this, void 0, void 0, function* () {
            for (const credential of this.credentials.values()) {
                if (credential.name === name) {
                    return credential;
                }
            }
            return null;
        });
    }
    findByType(type) {
        return __awaiter(this, void 0, void 0, function* () {
            return Array.from(this.credentials.values()).filter(cred => cred.type === type);
        });
    }
    create(credentialData) {
        return __awaiter(this, void 0, void 0, function* () {
            const id = `cred_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const credential = Object.assign(Object.assign({}, credentialData), { id, createdAt: new Date(), updatedAt: new Date() });
            this.credentials.set(id, credential);
            return credential;
        });
    }
    update(id, updates) {
        return __awaiter(this, void 0, void 0, function* () {
            const credential = this.credentials.get(id);
            if (!credential) {
                throw new Error(`Credential with ID ${id} not found`);
            }
            const updated = Object.assign(Object.assign(Object.assign({}, credential), updates), { updatedAt: new Date() });
            this.credentials.set(id, updated);
            return updated;
        });
    }
    delete(id) {
        return __awaiter(this, void 0, void 0, function* () {
            this.credentials.delete(id);
        });
    }
    testCredentials(request) {
        return __awaiter(this, void 0, void 0, function* () {
            // Mock implementation always returns OK
            return {
                status: 'OK',
                message: 'Credentials test successful (mock implementation)',
            };
        });
    }
}
exports.MockCredentialRepository = MockCredentialRepository;
class MockCredentialEncryption {
    encrypt(data) {
        return __awaiter(this, void 0, void 0, function* () {
            // Mock encryption - just base64 encode (NOT secure, for development only)
            return Buffer.from(JSON.stringify(data)).toString('base64');
        });
    }
    decrypt(encryptedData) {
        return __awaiter(this, void 0, void 0, function* () {
            // Mock decryption - just base64 decode (NOT secure, for development only)
            try {
                const jsonString = Buffer.from(encryptedData, 'base64').toString();
                return JSON.parse(jsonString);
            }
            catch (error) {
                throw new Error('Failed to decrypt data (mock encryption)');
            }
        });
    }
    hash(data) {
        return __awaiter(this, void 0, void 0, function* () {
            // Mock hash - NOT secure, for development only
            return Buffer.from(data).toString('base64');
        });
    }
    verify(data, hash) {
        return __awaiter(this, void 0, void 0, function* () {
            const expectedHash = yield this.hash(data);
            return expectedHash === hash;
        });
    }
}
exports.MockCredentialEncryption = MockCredentialEncryption;
//# sourceMappingURL=credential.service.js.map