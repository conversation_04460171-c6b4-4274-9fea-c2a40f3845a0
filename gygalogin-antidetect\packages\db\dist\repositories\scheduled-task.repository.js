"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScheduledTaskRepository = void 0;
const decorators_1 = require("@gygalogin/decorators");
const typeorm_1 = require("typeorm");
const scheduled_task_entity_1 = require("../entities/scheduled-task.entity");
let ScheduledTaskRepository = class ScheduledTaskRepository extends typeorm_1.Repository {
    constructor(dataSource) {
        super(scheduled_task_entity_1.ScheduledTaskEntity, dataSource.manager);
    }
    /**
     * Find entity by ID - added for compatibility with core package
     */
    async findById(id) {
        return this.findOne({ where: { id } });
    }
    /**
     * Find all entities with options - added for compatibility with core package
     */
    async findAll(options) {
        return this.find(options);
    }
    /**
     * Create a new scheduled task entity and return it
     */
    async createTask(taskData) {
        const task = this.create(taskData);
        return this.save(task);
    }
    /**
     * Update scheduled task and return the updated entity
     */
    async updateTask(id, updateData) {
        await this.update(id, updateData);
        return this.findOneBy({ id });
    }
    /**
     * Delete scheduled task by id and return success status
     */
    async deleteTask(id) {
        const result = await this.delete(id);
        return (result.affected ?? 0) > 0;
    }
    /**
     * Find scheduled tasks by workflow ID
     */
    async findByWorkflowId(workflowId) {
        return this.find({
            where: { workflowId },
            relations: ['workflow'],
        });
    }
    /**
     * Find active scheduled tasks
     */
    async findActiveTasks() {
        return this.find({
            where: { isActive: true },
            relations: ['workflow'],
        });
    }
    /**
     * Find scheduled task by workflow ID and node name
     */
    async findByWorkflowAndNode(workflowId, nodeName) {
        return this.findOne({
            where: { workflowId, nodeName },
            relations: ['workflow'],
        });
    }
    /**
     * Deactivate all scheduled tasks for a workflow
     */
    async deactivateByWorkflowId(workflowId) {
        await this.update({ workflowId }, { isActive: false });
    }
    /**
     * Delete all scheduled tasks for a workflow
     */
    async deleteByWorkflowId(workflowId) {
        await this.delete({ workflowId });
    }
    /**
     * Search scheduled tasks with criteria
     */
    async searchTasks(criteria) {
        const queryBuilder = this.createQueryBuilder('scheduledTask').leftJoinAndSelect('scheduledTask.workflow', 'workflow');
        if (criteria.workflowId) {
            queryBuilder.andWhere('scheduledTask.workflowId = :workflowId', {
                workflowId: criteria.workflowId,
            });
        }
        if (criteria.nodeName) {
            queryBuilder.andWhere('scheduledTask.nodeName = :nodeName', { nodeName: criteria.nodeName });
        }
        if (criteria.isActive !== undefined) {
            queryBuilder.andWhere('scheduledTask.isActive = :isActive', { isActive: criteria.isActive });
        }
        return queryBuilder.getMany();
    }
};
exports.ScheduledTaskRepository = ScheduledTaskRepository;
exports.ScheduledTaskRepository = ScheduledTaskRepository = __decorate([
    (0, decorators_1.Service)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], ScheduledTaskRepository);
//# sourceMappingURL=scheduled-task.repository.js.map