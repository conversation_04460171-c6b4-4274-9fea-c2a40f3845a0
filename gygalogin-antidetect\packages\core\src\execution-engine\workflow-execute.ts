/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-console */

import { ExecutionRepository, WorkflowRepository } from '@gygalogin/db';
import { INode, INodeExecutionData, Workflow, WorkflowExecuteMode } from '@gygalogin/shared';
import { buildAdjacencyList, detectCycle } from '@gygalogin/shared/dist/utils/graph-utils';
import EventEmitter from 'eventemitter3';
import PCancelable, { CancelError } from 'p-cancelable';
import { NodeExecuteFunctions } from '../node-execution-functions';
import { ExecutionLifecycleHooks } from './execution-lifecycle-hooks';
import { GraphRunner } from './graph-runner';
import { ExecuteContext } from './node-execution-context/execute-context';
import { WorkflowNodeContext } from './node-execution-context/workflow-node-context';
import { NodeExecutor } from './node-executor';
import { WorkflowStateManager } from './workflow-state-manager';

export type WorkflowExecuteEvents = {
  beforeWorkflowStart: [];
  afterWorkflowEnd: [];
  beforeNodeExecute: [INode];
  afterNodeExecute: [INode, INodeExecutionData[]];
  onNodeError: [INode, Error];
};

export class WorkflowExecute {
  /**
   * Factory method to create WorkflowExecute instance with database integration
   */
  static async createFromDatabase(
    workflowId: string,
    workflowRepo: WorkflowRepository,
    executionRepo: ExecutionRepository,
    nodeTypes: any
  ): Promise<WorkflowExecute> {
    // Load workflow from database
    const workflowEntity = await workflowRepo.findById(workflowId);
    if (!workflowEntity) {
      throw new Error(`Workflow with ID ${workflowId} not found in database`);
    }

    // Convert database entity to Workflow instance
    const workflow = new Workflow({
      id: workflowEntity.id,
      name: workflowEntity.name,
      nodes: workflowEntity.nodes,
      connections: workflowEntity.connections,
      nodeTypes,
    });

    return new WorkflowExecute(workflow, workflowRepo, executionRepo);
  }

  private readonly emitter = new EventEmitter<WorkflowExecuteEvents>();
  public on = this.emitter.on.bind(this.emitter);
  public once = this.emitter.once.bind(this.emitter);
  public off = this.emitter.off.bind(this.emitter);
  public emit = this.emitter.emit.bind(this.emitter);
  public hooks: ExecutionLifecycleHooks;

  private workflow: Workflow;
  private controller: AbortController;
  private multiInputNodes: Set<string>;
  private waitingNodes: Map<string, { received: Record<string, INodeExecutionData[]> }>;
  private currentExecutionId?: string;

  constructor(
    workflow: Workflow,
    private workflowRepo?: WorkflowRepository,
    private executionRepo?: ExecutionRepository
  ) {
    this.workflow = workflow;
    this.controller = new AbortController();
    this.multiInputNodes = new Set();
    this.waitingNodes = new Map();
    this.hooks = new ExecutionLifecycleHooks(workflow.id, workflow.toJSON());
  }

  public cancel(): void {
    this.controller.abort();
  }

  public run(): PCancelable<void> {
    return new PCancelable((resolve, reject, onCancel) => {
      onCancel(() => this.controller.abort());

      this.emitter.emit('beforeWorkflowStart');

      const runAsync = async () => {
        let stateManager: WorkflowStateManager | undefined;

        try {
          // Start execution logging if repository is available
          if (this.executionRepo) {
            const execution = await this.executionRepo.startExecution(this.workflow.id);
            this.currentExecutionId = execution.id;
          }

          // Create execution ID if not available
          const executionId =
            this.currentExecutionId ||
            `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

          // Create state manager
          stateManager = new WorkflowStateManager(this.workflow.id, executionId);

          // --- START: Graph Validation ---
          // Validate the workflow structure before execution
          const adjacencyList = buildAdjacencyList(
            this.workflow.getAllNodes(),
            this.workflow.connections
          );
          const cycle = detectCycle(adjacencyList);
          if (cycle) {
            // Reject the promise directly to ensure the error is propagated
            return reject(new Error(`Workflow has a cycle: ${cycle.join(' -> ')}`));
          }
          // --- END: Graph Validation ---

          // Create enhanced execution context
          const executionContext = new ExecuteContext(
            executionId,
            this.workflow.id,
            WorkflowExecuteMode.MANUAL
          );

          this.hooks.runHook('workflowExecuteBefore', [this.workflow.toJSON()]);

          // Create and run graph runner
          const graphRunner = new GraphRunner(
            this.workflow,
            stateManager,
            executionContext,
            this.hooks,
            this.controller.signal
          );

          await graphRunner.run();

          // Mark execution as successful if repository is available
          if (this.executionRepo && this.currentExecutionId) {
            await this.executionRepo.markAsSuccess(this.currentExecutionId);
          }

          // Log execution summary
          if (stateManager) {
            console.log('Execution Summary:', stateManager.getExecutionSummary());
          }
        } catch (executionError) {
          console.error('Workflow execution failed:', executionError);

          // Mark execution as failed if repository is available
          if (this.executionRepo && this.currentExecutionId) {
            await this.executionRepo.markAsError(
              this.currentExecutionId,
              (executionError as Error).message
            );
          }

          throw executionError;
        } finally {
          this.hooks.runHook('workflowExecuteAfter', [
            this.workflow.toJSON(),
            stateManager?.getExecutionSummary() || {},
          ]);
          this.emitter.emit('afterWorkflowEnd');
        }
      };

      runAsync().then(resolve).catch(reject);
    });
  }

  public applyPairedItem(
    inputData: INodeExecutionData[],
    outputData: INodeExecutionData[]
  ): INodeExecutionData[] {
    if (!outputData || outputData.length === 0) {
      return outputData;
    }

    if (!inputData || inputData.length === 0) {
      return outputData.map(output => ({
        ...output,
        pairedItem: { item: 0 },
      }));
    }

    if (inputData.length === 1) {
      return outputData.map(output => ({
        ...output,
        pairedItem: { item: 0 },
      }));
    }

    if (inputData.length === outputData.length) {
      return outputData.map((output, index) => ({
        ...output,
        pairedItem: { item: index },
      }));
    }

    return outputData.map(output => ({
      ...output,
      pairedItem: { item: 0 },
    }));
  }

  private precomputeMultiInputNodes() {
    const allNodes = this.workflow.getAllNodes();
    const inputCounts: Record<string, number> = {};

    for (const node of allNodes) {
      inputCounts[node.id] = 0;
    }

    for (const conn of this.workflow.connections) {
      if (inputCounts[conn.targetNodeId] !== undefined) {
        inputCounts[conn.targetNodeId]++;
      }
    }

    for (const nodeId in inputCounts) {
      if (inputCounts[nodeId] > 1) {
        this.multiInputNodes.add(nodeId);
      }
    }
  }

  private processNodeOutput(
    sourceNodeId: string,
    outputData: INodeExecutionData[],
    executionQueue: { node: INode; inputData: INodeExecutionData[] }[]
  ) {
    const connections = this.workflow.connections.filter(c => c.sourceNodeId === sourceNodeId);

    for (const conn of connections) {
      const nextNode = this.workflow.getNode(conn.targetNodeId);
      if (!nextNode) continue;

      if (!this.multiInputNodes.has(nextNode.id)) {
        executionQueue.push({ node: nextNode, inputData: outputData });
        continue;
      }

      let waitingNode = this.waitingNodes.get(nextNode.id);
      if (!waitingNode) {
        waitingNode = { received: {} };
        this.waitingNodes.set(nextNode.id, waitingNode);
      }

      waitingNode.received[conn.targetInput] = outputData;

      const requiredInputs = this.workflow.connections
        .filter(c => c.targetNodeId === nextNode.id)
        .map(c => c.targetInput);
      const receivedInputs = Object.keys(waitingNode.received);

      if (requiredInputs.every(inputName => receivedInputs.includes(inputName))) {
        const combinedInputData = Object.values(waitingNode.received).flat();
        executionQueue.push({ node: nextNode, inputData: combinedInputData });
        this.waitingNodes.delete(nextNode.id);
      }
    }
  }

  public async execute(_mode: WorkflowExecuteMode): Promise<void> {
    const nodes = this.workflow.getAllNodes();
    const connections = this.workflow.connections;
    const adjacencyList = buildAdjacencyList(nodes, connections);

    const cycle = detectCycle(adjacencyList);
    if (cycle) {
      throw new Error(`Workflow has a cycle: ${cycle.join(' -> ')}`);
    }
  }

  public async executeNodeById(
    nodeId: string,
    inputData: INodeExecutionData[]
  ): Promise<INodeExecutionData[][]> {
    const node = this.workflow.getNode(nodeId);
    if (!node) {
      throw new Error(`Node with ID ${nodeId} not found`);
    }

    const executionContext = new ExecuteContext();
    const result = await this.executeNode(node, inputData, executionContext);
    return [result];
  }

  public async executeSubgraph(
    startNodeId: string,
    inputData: INodeExecutionData[]
  ): Promise<void> {
    const executionContext = new ExecuteContext();
    const executionQueue: { node: INode; inputData: INodeExecutionData[] }[] = [
      { node: this.workflow.getNode(startNodeId)!, inputData },
    ];

    while (executionQueue.length > 0) {
      if (this.controller.signal.aborted) {
        throw new CancelError('Workflow execution was canceled.');
      }

      const queueItem = executionQueue.shift();
      if (!queueItem) continue;

      const { node, inputData } = queueItem;

      try {
        this.hooks.runHook('nodeExecuteBefore', [node]);
        const outputData = await this.executeNode(node, inputData, executionContext);
        this.hooks.runHook('nodeExecuteAfter', [node, outputData]);

        const outputDataWithPairedItems = this.applyPairedItem(inputData, outputData);

        executionContext.setNodeOutput(node.id, outputDataWithPairedItems);

        this.processNodeOutput(node.id, outputDataWithPairedItems, executionQueue);
      } catch (error) {
        if (error instanceof CancelError) {
          throw error;
        }

        this.hooks.runHook('onNodeError', [node, error as Error]);
        throw error;
      }
    }
  }

  public hasCycles(): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycleUtil = (nodeId: string): boolean => {
      visited.add(nodeId);
      recursionStack.add(nodeId);

      const nextNodes = this.workflow.findNextNodes(nodeId);
      for (const nextNode of nextNodes) {
        if (!visited.has(nextNode.id)) {
          if (hasCycleUtil(nextNode.id)) {
            return true;
          }
        } else if (recursionStack.has(nextNode.id)) {
          return true;
        }
      }

      recursionStack.delete(nodeId);
      return false;
    };

    for (const node of this.workflow.getAllNodes()) {
      if (!visited.has(node.id)) {
        if (hasCycleUtil(node.id)) {
          return true;
        }
      }
    }

    return false;
  }

  public filterDisabledNodes(): INode[] {
    return this.workflow.getAllNodes().filter(node => !node.disabled);
  }

  // New methods to access state management functionality
  public runWithStateManager(
    runMode: WorkflowExecuteMode = WorkflowExecuteMode.MANUAL
  ): PCancelable<WorkflowStateManager> {
    return new PCancelable((resolve, reject, onCancel) => {
      onCancel(() => this.controller.abort());

      const runAsync = async () => {
        let stateManager: WorkflowStateManager | undefined;

        try {
          // Start execution logging if repository is available
          if (this.executionRepo) {
            const execution = await this.executionRepo.startExecution(this.workflow.id);
            this.currentExecutionId = execution.id;
          }

          // Create execution ID if not available
          const executionId =
            this.currentExecutionId ||
            `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

          // Create state manager
          stateManager = new WorkflowStateManager(this.workflow.id, executionId);

          // Create enhanced execution context
          const executionContext = new ExecuteContext(executionId, this.workflow.id, runMode);

          this.hooks.runHook('workflowExecuteBefore', [this.workflow.toJSON()]);

          // Create and run graph runner
          const graphRunner = new GraphRunner(
            this.workflow,
            stateManager,
            executionContext,
            this.hooks,
            this.controller.signal
          );

          await graphRunner.run();

          // Mark execution as successful if repository is available
          if (this.executionRepo && this.currentExecutionId) {
            await this.executionRepo.markAsSuccess(this.currentExecutionId);
          }

          return stateManager;
        } catch (executionError) {
          console.error('Workflow execution failed:', executionError);

          // Mark execution as failed if repository is available
          if (this.executionRepo && this.currentExecutionId) {
            await this.executionRepo.markAsError(
              this.currentExecutionId,
              (executionError as Error).message
            );
          }

          throw executionError;
        } finally {
          this.hooks.runHook('workflowExecuteAfter', [
            this.workflow.toJSON(),
            stateManager?.getExecutionSummary() || {},
          ]);
        }
      };

      runAsync().then(resolve).catch(reject);
    });
  }

  // Method to get a fresh NodeExecutor instance
  public createNodeExecutor(
    stateManager: WorkflowStateManager,
    abortSignal?: AbortSignal
  ): NodeExecutor {
    return new NodeExecutor(this.workflow, stateManager, abortSignal || this.controller.signal);
  }

  // Method to create a fresh execution context
  public createExecutionContext(
    executionId?: string,
    runMode: WorkflowExecuteMode = WorkflowExecuteMode.MANUAL
  ): ExecuteContext {
    const execId = executionId || `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return new ExecuteContext(execId, this.workflow.id, runMode);
  }

  // Method to create a fresh GraphRunner
  public createGraphRunner(
    stateManager: WorkflowStateManager,
    context: ExecuteContext,
    abortSignal?: AbortSignal
  ): GraphRunner {
    return new GraphRunner(
      this.workflow,
      stateManager,
      context,
      this.hooks,
      abortSignal || this.controller.signal
    );
  }

  // Legacy method kept for backwards compatibility
  private async executeNode(
    node: INode,
    inputData: INodeExecutionData[],
    context: ExecuteContext
  ): Promise<INodeExecutionData[]> {
    if (this.controller.signal.aborted) {
      throw new CancelError('Workflow execution was canceled.');
    }

    const nodeType = this.workflow.nodeTypes?.getByNameAndVersion(node.type, node.typeVersion || 1);
    if (!nodeType) {
      throw new Error(`Node type "${node.type}" not found.`);
    }

    const nodeContext = new WorkflowNodeContext(this.workflow, node, context.getAll());
    const executeFunctions = new NodeExecuteFunctions(nodeContext, node, inputData);

    const result = await nodeType.execute!.call(executeFunctions);

    if (Array.isArray(result) && Array.isArray(result[0])) {
      return (result as INodeExecutionData[][]).flat();
    }

    return result as INodeExecutionData[];
  }
}
