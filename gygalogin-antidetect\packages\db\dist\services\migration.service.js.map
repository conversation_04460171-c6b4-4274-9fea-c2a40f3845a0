{"version": 3, "file": "migration.service.js", "sourceRoot": "", "sources": ["../../src/services/migration.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,sDAAgD;AAChD,yDAAqD;AACrD,8CAA2C;AAGpC,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YACmB,eAAgC,EAChC,MAAc;QADd,oBAAe,GAAf,eAAe,CAAiB;QAChC,WAAM,GAAN,MAAM,CAAQ;IAC9B,CAAC;IAEJ,KAAK,CAAC,aAAa;QACjB,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;QAExD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,aAAa,EAAE,CAAC;YACpD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,UAAU,CAAC,MAAM,aAAa,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;QAExD,IAAI,CAAC;YACH,MAAM,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;QACxD,MAAM,oBAAoB,GAAG,MAAM,UAAU,CAAC,cAAc,EAAE,CAAC;QAE/D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAElF,uDAAuD;QACvD,IAAI,CAAC;YACH,MAAM,kBAAkB,GAAG,MAAM,UAAU,CAAC,aAAa,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC;YACnF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,kBAAkB,CAAC,MAAM,EAAE,CAAC,CAAC;QAChF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;CACF,CAAA;AA5CY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,oBAAO,GAAE;qCAG4B,kCAAe;QACxB,eAAM;GAHtB,gBAAgB,CA4C5B"}