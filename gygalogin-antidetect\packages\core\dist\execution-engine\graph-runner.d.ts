import { INode, INodeExecutionData, Workflow } from '@gygalogin/shared';
import { ExecuteContext } from './node-execution-context/execute-context';
import { WorkflowStateManager } from './workflow-state-manager';
import { INodeExecutorOptions } from './node-executor';
import { ExecutionLifecycleHooks } from './execution-lifecycle-hooks';
export interface IExecutionQueueItem {
    node: INode;
    inputData: INodeExecutionData[];
    options?: INodeExecutorOptions;
}
export declare class GraphRunner {
    private workflow;
    private stateManager;
    private context;
    private hooks;
    private abortSignal?;
    private executionQueue;
    private multiInputNodes;
    private nodeExecutor;
    constructor(workflow: Workflow, stateManager: WorkflowStateManager, context: ExecuteContext, hooks: ExecutionLifecycleHooks, abortSignal?: AbortSignal | undefined);
    run(): Promise<void>;
    private processQueueItem;
    private handleNodeError;
    private processNodeOutput;
    private handleMultiInputNode;
    private addToQueue;
    private precomputeMultiInputNodes;
    getExecutionQueue(): IExecutionQueueItem[];
    getQueueLength(): number;
    clearQueue(): void;
    isQueueEmpty(): boolean;
    enqueueNode(node: INode, inputData: INodeExecutionData[], options?: INodeExecutorOptions): void;
    getMultiInputNodes(): Set<string>;
}
//# sourceMappingURL=graph-runner.d.ts.map