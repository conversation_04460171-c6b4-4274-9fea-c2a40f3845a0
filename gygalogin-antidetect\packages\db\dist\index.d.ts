import 'reflect-metadata';
import { DataSource, Repository } from 'typeorm';
import { ProfileEntity } from './entities/profile.entity';
import { ProxyEntity } from './entities/proxy.entity';
import { SessionEntity } from './entities/session.entity';
import { WorkflowEntity } from './entities/workflow.entity';
import { ExecutionEntity } from './entities/execution.entity';
import { ScheduledTaskEntity } from './entities/scheduled-task.entity';
import { ProfileRepository } from './repositories/profile.repository';
import { ProxyRepository } from './repositories/proxy.repository';
import { WorkflowRepository } from './repositories/workflow.repository';
import { ExecutionRepository } from './repositories/execution.repository';
import { ScheduledTaskRepository } from './repositories/scheduled-task.repository';
import { BaseRepository } from './repositories/base.repository';
import { DatabaseService } from './services/database.service';
import { MigrationService } from './services/migration.service';
import { TransactionService } from './services/transaction.service';
declare class SessionRepository extends BaseRepository<SessionEntity> {
    constructor(repository: Repository<SessionEntity>);
}
export declare class DB {
    private profileRepository;
    private proxyRepository;
    private sessionRepository;
    private workflowRepository;
    private executionRepository;
    private taskRepository;
    private _dataSource;
    constructor(dataSource: DataSource);
    initialize(): Promise<void>;
    close(): Promise<void>;
    get profiles(): ProfileRepository;
    get proxies(): ProxyRepository;
    get sessions(): SessionRepository;
    get workflows(): WorkflowRepository;
    get executions(): ExecutionRepository;
    get tasks(): ScheduledTaskRepository;
    get dataSource(): DataSource;
}
export declare const database: DB;
export { ProfileEntity, ProxyEntity, SessionEntity, WorkflowEntity, ExecutionEntity, ScheduledTaskEntity };
export { BaseRepository, ProfileRepository, ProxyRepository, WorkflowRepository, ExecutionRepository, ScheduledTaskRepository, };
export { DatabaseService, MigrationService, TransactionService, };
export type { ProfileSearchCriteria } from './repositories/profile.repository';
export type { ProxySearchCriteria } from './repositories/proxy.repository';
export type { ScheduledTaskSearchCriteria } from './repositories/scheduled-task.repository';
export type { WorkflowSearchCriteria } from './repositories/workflow.repository';
export type { ExecutionSearchCriteria } from './repositories/execution.repository';
export { ExecutionStatus, ExecutionMode } from './entities/execution.entity';
export type { TaskStatus, TaskType } from './entities/scheduled-task.entity';
export { AppDataSource, DataSourceProvider } from './connection';
//# sourceMappingURL=index.d.ts.map