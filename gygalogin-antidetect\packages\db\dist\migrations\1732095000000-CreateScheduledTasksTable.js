"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateScheduledTasksTable1732095000000 = void 0;
class CreateScheduledTasksTable1732095000000 {
    constructor() {
        this.name = 'CreateScheduledTasksTable1732095000000';
    }
    async up(queryRunner) {
        // Create the scheduled_tasks table with foreign key constraint
        await queryRunner.query(`
      CREATE TABLE "scheduled_tasks" (
        "id" varchar PRIMARY KEY NOT NULL DEFAULT (uuid()),
        "workflowId" varchar NOT NULL,
        "nodeName" varchar NOT NULL,
        "cronTime" varchar NOT NULL,
        "triggerTime" text,
        "isActive" boolean NOT NULL DEFAULT (true),
        "createdAt" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP),
        "updatedAt" datetime NOT NULL DEFAULT (CURRENT_TIMESTAMP),
        FOREIGN KEY ("workflowId") REFERENCES "workflows" ("id") ON DELETE CASCADE
      )
    `);
        // Create indexes for better query performance
        await queryRunner.query('CREATE INDEX "IDX_SCHEDULED_TASKS_WORKFLOW_ID" ON "scheduled_tasks" ("workflowId")');
        await queryRunner.query('CREATE INDEX "IDX_SCHEDULED_TASKS_IS_ACTIVE" ON "scheduled_tasks" ("isActive")');
        await queryRunner.query('CREATE UNIQUE INDEX "IDX_SCHEDULED_TASKS_WORKFLOW_NODE" ON "scheduled_tasks" ("workflowId", "nodeName")');
    }
    async down(queryRunner) {
        // Drop indexes
        await queryRunner.query('DROP INDEX "IDX_SCHEDULED_TASKS_WORKFLOW_NODE"');
        await queryRunner.query('DROP INDEX "IDX_SCHEDULED_TASKS_IS_ACTIVE"');
        await queryRunner.query('DROP INDEX "IDX_SCHEDULED_TASKS_WORKFLOW_ID"');
        // Drop foreign key
        const table = await queryRunner.getTable('scheduled_tasks');
        if (table) {
            const foreignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('workflowId') !== -1);
            if (foreignKey) {
                await queryRunner.dropForeignKey('scheduled_tasks', foreignKey);
            }
        }
        // Drop table
        await queryRunner.dropTable('scheduled_tasks');
    }
}
exports.CreateScheduledTasksTable1732095000000 = CreateScheduledTasksTable1732095000000;
//# sourceMappingURL=1732095000000-CreateScheduledTasksTable.js.map