import { WorkflowEntity } from './workflow.entity';
export type TaskStatus = 'active' | 'inactive' | 'completed' | 'failed' | 'running';
export type TaskType = 'cron' | 'interval' | 'polling';
export declare class ScheduledTaskEntity {
    id: string;
    workflowId: string;
    workflow: WorkflowEntity;
    nodeName: string;
    cronTime: string;
    triggerTime: object;
    isActive: boolean;
    name?: string;
    description?: string;
    type: TaskType;
    status: TaskStatus;
    cronExpression?: string;
    intervalMs?: number;
    pollUrl?: string;
    pollIntervalMs?: number;
    nextRunAt?: Date;
    lastRunAt?: Date;
    runCount: number;
    successCount: number;
    failureCount: number;
    lastError?: string;
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
    isTaskActive(): boolean;
    canRun(): boolean;
    markAsRunning(): void;
    markAsCompleted(): void;
    markAsFailed(error: string): void;
    private updateNextRunTime;
}
//# sourceMappingURL=scheduled-task.entity.d.ts.map