"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataSourceProvider = exports.AppDataSource = void 0;
const decorators_1 = require("@gygalogin/decorators");
const typeorm_1 = require("typeorm");
const profile_entity_1 = require("./entities/profile.entity");
const proxy_entity_1 = require("./entities/proxy.entity");
const session_entity_1 = require("./entities/session.entity");
const workflow_entity_1 = require("./entities/workflow.entity");
const execution_entity_1 = require("./entities/execution.entity");
const scheduled_task_entity_1 = require("./entities/scheduled-task.entity");
// Keep existing AppDataSource for backward compatibility
exports.AppDataSource = new typeorm_1.DataSource({
    type: 'better-sqlite3',
    database: 'gygalogin.sqlite',
    synchronize: false,
    logging: false,
    entities: [
        profile_entity_1.ProfileEntity,
        proxy_entity_1.ProxyEntity,
        session_entity_1.SessionEntity,
        workflow_entity_1.WorkflowEntity,
        execution_entity_1.ExecutionEntity,
        scheduled_task_entity_1.ScheduledTaskEntity,
    ],
    migrations: ['src/migrations/*.ts'],
    subscribers: [],
});
// NEW: DI-compatible DataSource provider
let DataSourceProvider = class DataSourceProvider extends typeorm_1.DataSource {
};
exports.DataSourceProvider = DataSourceProvider;
exports.DataSourceProvider = DataSourceProvider = __decorate([
    (0, decorators_1.Service)({ factory: () => exports.AppDataSource })
], DataSourceProvider);
//# sourceMappingURL=connection.js.map