"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorMonitor = void 0;
/**
 * Error monitoring and alerting system
 */
class ErrorMonitor {
    constructor(config) {
        this.config = config;
        this.occurrences = [];
        this.patterns = [];
        this.alerts = [];
        this.alertHandlers = [];
        this.stats = this.initializeStats();
        this.initializeDefaultPatterns();
        this.addCustomPatterns();
        this.alertHandlers = [...config.alertHandlers];
        this.startCleanupInterval();
    }
    /**
     * Record an error occurrence
     */
    recordError(error, nodeId, nodeType, workflowId, executionId, recoveryResult, metadata) {
        const occurrence = {
            id: this.generateOccurrenceId(),
            timestamp: new Date(),
            error,
            nodeId,
            nodeType,
            workflowId,
            executionId,
            recoveryResult,
            metadata
        };
        this.occurrences.push(occurrence);
        this.updateStats(occurrence);
        // Trim old occurrences if necessary
        if (this.occurrences.length > this.config.maxOccurrenceHistory) {
            this.occurrences = this.occurrences.slice(-this.config.maxOccurrenceHistory);
        }
        // Check for pattern matches
        if (this.config.enableRealTimeMonitoring) {
            this.checkPatterns(occurrence);
        }
    }
    /**
     * Listen to error recovery events
     */
    handleRecoveryEvent(event) {
        if (event.type === 'recovery_started') {
            this.recordError(event.error, event.nodeId, 'unknown', // nodeType not available in event
            'unknown', // workflowId not available in event
            'unknown', // executionId not available in event
            undefined, { recoveryEvent: true });
        }
        else if (event.type === 'recovery_completed' || event.type === 'recovery_failed') {
            // Update the latest occurrence with recovery result
            const latestOccurrence = this.occurrences
                .reverse()
                .find(occ => occ.nodeId === event.nodeId && occ.error.message === event.error.message);
            if (latestOccurrence) {
                latestOccurrence.recoveryResult = event.result;
                latestOccurrence.metadata = Object.assign(Object.assign({}, latestOccurrence.metadata), { recoveryElapsedTime: event.elapsedTime, recoveryStrategy: event.strategy });
            }
            this.occurrences.reverse(); // Restore order
        }
    }
    /**
     * Add a custom error pattern
     */
    addPattern(pattern) {
        const existingIndex = this.patterns.findIndex(p => p.id === pattern.id);
        if (existingIndex >= 0) {
            this.patterns[existingIndex] = pattern;
        }
        else {
            this.patterns.push(pattern);
        }
    }
    /**
     * Remove an error pattern
     */
    removePattern(patternId) {
        const initialLength = this.patterns.length;
        this.patterns = this.patterns.filter(p => p.id !== patternId);
        return this.patterns.length < initialLength;
    }
    /**
     * Get all error patterns
     */
    getPatterns() {
        return [...this.patterns];
    }
    /**
     * Get recent error occurrences
     */
    getRecentOccurrences(limit, timeWindow) {
        let occurrences = [...this.occurrences];
        if (timeWindow) {
            const cutoff = Date.now() - timeWindow;
            occurrences = occurrences.filter(occ => occ.timestamp.getTime() > cutoff);
        }
        if (limit) {
            occurrences = occurrences.slice(-limit);
        }
        return occurrences.reverse(); // Most recent first
    }
    /**
     * Get error statistics
     */
    getStats() {
        return Object.assign({}, this.stats);
    }
    /**
     * Get recent alerts
     */
    getAlerts(limit) {
        const alerts = limit ? this.alerts.slice(-limit) : [...this.alerts];
        return alerts.reverse(); // Most recent first
    }
    /**
     * Clear all alerts
     */
    clearAlerts() {
        this.alerts = [];
    }
    /**
     * Add an alert handler
     */
    addAlertHandler(handler) {
        this.alertHandlers.push(handler);
    }
    /**
     * Remove an alert handler
     */
    removeAlertHandler(handlerName) {
        const initialLength = this.alertHandlers.length;
        this.alertHandlers = this.alertHandlers.filter(h => h.name !== handlerName);
        return this.alertHandlers.length < initialLength;
    }
    /**
     * Generate error summary report
     */
    generateReport(timeWindow) {
        const occurrences = this.getRecentOccurrences(undefined, timeWindow);
        // Group errors by message
        const errorGroups = {};
        occurrences.forEach(occ => {
            const message = occ.error.message;
            if (!errorGroups[message]) {
                errorGroups[message] = { count: 0, nodes: new Set() };
            }
            errorGroups[message].count++;
            errorGroups[message].nodes.add(occ.nodeId);
        });
        const topErrors = Object.entries(errorGroups)
            .map(([message, data]) => ({
            message,
            count: data.count,
            nodes: Array.from(data.nodes)
        }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
        const recommendations = this.generateRecommendations(occurrences);
        return {
            summary: this.getStats(),
            topErrors,
            recommendations
        };
    }
    /**
     * Cleanup resources
     */
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = undefined;
        }
    }
    initializeDefaultPatterns() {
        const defaultPatterns = [
            {
                id: 'high-frequency-errors',
                name: 'High Frequency Errors',
                description: 'Too many errors in a short time period',
                pattern: () => true, // Matches all errors
                threshold: { count: 10, timeWindow: 60000 }, // 10 errors in 1 minute
                severity: 'high',
                actions: [
                    { type: 'log', config: { level: 'warn' } },
                    { type: 'alert', config: { message: 'High error frequency detected' } }
                ]
            },
            {
                id: 'credential-errors',
                name: 'Credential Errors',
                description: 'Authentication and credential-related errors',
                pattern: /credential|authentication|unauthorized|forbidden/i,
                threshold: { count: 3, timeWindow: 300000 }, // 3 errors in 5 minutes
                severity: 'critical',
                actions: [
                    { type: 'alert', config: { message: 'Credential issues detected' } },
                    { type: 'log', config: { level: 'error' } }
                ]
            },
            {
                id: 'network-timeouts',
                name: 'Network Timeouts',
                description: 'Network timeout and connection errors',
                pattern: /timeout|connection|network|econnreset|econnrefused/i,
                threshold: { count: 5, timeWindow: 180000 }, // 5 errors in 3 minutes
                severity: 'medium',
                actions: [
                    { type: 'log', config: { level: 'warn' } },
                    { type: 'alert', config: { message: 'Network connectivity issues detected' } }
                ]
            },
            {
                id: 'rate-limiting',
                name: 'Rate Limiting',
                description: 'Rate limiting errors from external services',
                pattern: /rate limit|too many requests|429/i,
                threshold: { count: 3, timeWindow: 600000 }, // 3 errors in 10 minutes
                severity: 'medium',
                actions: [
                    { type: 'log', config: { level: 'info' } },
                    { type: 'alert', config: { message: 'Rate limiting detected, consider adjusting request frequency' } }
                ]
            }
        ];
        defaultPatterns.forEach(pattern => this.addPattern(pattern));
    }
    addCustomPatterns() {
        if (this.config.customPatterns) {
            this.config.customPatterns.forEach(pattern => this.addPattern(pattern));
        }
    }
    checkPatterns(newOccurrence) {
        for (const pattern of this.patterns) {
            const matches = this.patternMatches(pattern, newOccurrence);
            if (!matches)
                continue;
            // Get recent occurrences that match this pattern
            const matchingOccurrences = this.getMatchingOccurrences(pattern);
            if (matchingOccurrences.length >= pattern.threshold.count) {
                this.triggerAlert(pattern, matchingOccurrences);
            }
        }
    }
    patternMatches(pattern, occurrence) {
        if (pattern.pattern instanceof RegExp) {
            return pattern.pattern.test(occurrence.error.message);
        }
        else if (typeof pattern.pattern === 'function') {
            return pattern.pattern(occurrence.error);
        }
        return false;
    }
    getMatchingOccurrences(pattern) {
        const cutoff = Date.now() - pattern.threshold.timeWindow;
        return this.occurrences.filter(occ => {
            return occ.timestamp.getTime() > cutoff && this.patternMatches(pattern, occ);
        });
    }
    triggerAlert(pattern, occurrences) {
        return __awaiter(this, void 0, void 0, function* () {
            const alert = {
                id: this.generateAlertId(),
                patternId: pattern.id,
                patternName: pattern.name,
                severity: pattern.severity,
                timestamp: new Date(),
                message: `${pattern.name}: ${pattern.description} (${occurrences.length} occurrences)`,
                occurrences,
                affectedNodes: [...new Set(occurrences.map(occ => occ.nodeId))],
                affectedWorkflows: [...new Set(occurrences.map(occ => occ.workflowId))],
                recommendations: this.generatePatternRecommendations(pattern, occurrences)
            };
            this.alerts.push(alert);
            // Execute pattern actions
            for (const action of pattern.actions) {
                if (action.condition && !action.condition(occurrences)) {
                    continue;
                }
                yield this.executePatternAction(action, alert);
            }
            // Notify alert handlers
            for (const handler of this.alertHandlers) {
                if (handler.canHandle && !handler.canHandle(alert)) {
                    continue;
                }
                try {
                    yield handler.handle(alert);
                }
                catch (error) {
                    console.error(`Alert handler ${handler.name} failed:`, error);
                }
            }
        });
    }
    executePatternAction(action, alert) {
        return __awaiter(this, void 0, void 0, function* () {
            switch (action.type) {
                case 'log':
                    const level = action.config.level || 'info';
                    this.logMessage(level, `[ErrorMonitor] ${alert.message}`);
                    break;
                case 'alert':
                    console.warn(`[ALERT] ${action.config.message || alert.message}`);
                    break;
                // Add more action types as needed (webhook, email, etc.)
                default:
                    console.warn(`Unknown error pattern action type: ${action.type}`);
            }
        });
    }
    logMessage(level, message) {
        switch (level) {
            case 'error':
                console.error(message);
                break;
            case 'warn':
                console.warn(message);
                break;
            case 'info':
                console.info(message);
                break;
            case 'debug':
                console.debug(message);
                break;
            default:
                console.log(message);
        }
    }
    generatePatternRecommendations(pattern, occurrences) {
        const recommendations = [];
        if (pattern.id === 'credential-errors') {
            recommendations.push('Check credential configuration and validity');
            recommendations.push('Verify API keys and authentication tokens');
        }
        else if (pattern.id === 'network-timeouts') {
            recommendations.push('Consider increasing timeout values');
            recommendations.push('Check network connectivity and latency');
        }
        else if (pattern.id === 'rate-limiting') {
            recommendations.push('Implement request throttling');
            recommendations.push('Consider using exponential backoff');
        }
        return recommendations;
    }
    generateRecommendations(occurrences) {
        const recommendations = [];
        // Analyze error patterns and generate recommendations
        const errorTypes = new Set(occurrences.map(occ => occ.error.constructor.name));
        const errorMessages = occurrences.map(occ => occ.error.message.toLowerCase());
        // Check for timeout/network related errors
        if (errorTypes.has('NetworkError') ||
            errorTypes.has('TimeoutError') ||
            errorTypes.has('NodeTimeoutError') ||
            errorMessages.some(msg => msg.includes('timeout') || msg.includes('network') || msg.includes('connection'))) {
            recommendations.push('Consider implementing retry logic with exponential backoff');
            recommendations.push('Review network configuration and timeouts');
        }
        if (errorTypes.has('CredentialError')) {
            recommendations.push('Audit credential configurations');
            recommendations.push('Implement credential rotation procedures');
        }
        if (errorTypes.has('ServiceUnavailableError') ||
            errorMessages.some(msg => msg.includes('unavailable') || msg.includes('503'))) {
            recommendations.push('Implement circuit breaker patterns');
            recommendations.push('Consider service health checks and monitoring');
        }
        if (errorTypes.has('RateLimitError') ||
            errorMessages.some(msg => msg.includes('rate limit') || msg.includes('429'))) {
            recommendations.push('Implement request throttling and rate limiting');
            recommendations.push('Consider using exponential backoff for retries');
        }
        return recommendations;
    }
    updateStats(occurrence) {
        this.stats.totalErrors++;
        const errorType = occurrence.error.constructor.name;
        this.stats.errorsByType[errorType] = (this.stats.errorsByType[errorType] || 0) + 1;
        this.stats.errorsByNode[occurrence.nodeId] = (this.stats.errorsByNode[occurrence.nodeId] || 0) + 1;
        this.stats.errorsByWorkflow[occurrence.workflowId] = (this.stats.errorsByWorkflow[occurrence.workflowId] || 0) + 1;
        // Update time window stats (simplified)
        const now = new Date();
        const currentWindow = this.stats.errorsByTimeWindow.find(w => Math.abs(w.timestamp.getTime() - now.getTime()) < 60000);
        if (currentWindow) {
            currentWindow.count++;
            currentWindow.errorTypes[errorType] = (currentWindow.errorTypes[errorType] || 0) + 1;
        }
        else {
            this.stats.errorsByTimeWindow.push({
                timestamp: now,
                count: 1,
                errorTypes: { [errorType]: 1 }
            });
            // Keep only recent windows
            const cutoff = Date.now() - 3600000; // 1 hour
            this.stats.errorsByTimeWindow = this.stats.errorsByTimeWindow.filter(w => w.timestamp.getTime() > cutoff);
        }
    }
    startCleanupInterval() {
        this.cleanupInterval = setInterval(() => {
            const cutoff = Date.now() - this.config.cleanupInterval;
            this.occurrences = this.occurrences.filter(occ => occ.timestamp.getTime() > cutoff);
            // Also cleanup old alerts
            this.alerts = this.alerts.filter(alert => alert.timestamp.getTime() > cutoff);
        }, this.config.cleanupInterval);
    }
    initializeStats() {
        return {
            totalErrors: 0,
            errorsByType: {},
            errorsByNode: {},
            errorsByWorkflow: {},
            errorsByTimeWindow: [],
            recoverySuccessRate: 0,
            averageRecoveryTime: 0,
            topErrorPatterns: []
        };
    }
    generateOccurrenceId() {
        return `occ_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    generateAlertId() {
        return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.ErrorMonitor = ErrorMonitor;
//# sourceMappingURL=error-monitor.js.map