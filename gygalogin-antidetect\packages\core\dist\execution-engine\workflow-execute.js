"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-console */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowExecute = void 0;
const shared_1 = require("@gygalogin/shared");
const graph_utils_1 = require("@gygalogin/shared/dist/utils/graph-utils");
const eventemitter3_1 = __importDefault(require("eventemitter3"));
const p_cancelable_1 = __importStar(require("p-cancelable"));
const node_execution_functions_1 = require("../node-execution-functions");
const execution_lifecycle_hooks_1 = require("./execution-lifecycle-hooks");
const graph_runner_1 = require("./graph-runner");
const execute_context_1 = require("./node-execution-context/execute-context");
const workflow_node_context_1 = require("./node-execution-context/workflow-node-context");
const node_executor_1 = require("./node-executor");
const workflow_state_manager_1 = require("./workflow-state-manager");
class WorkflowExecute {
    /**
     * Factory method to create WorkflowExecute instance with database integration
     */
    static createFromDatabase(workflowId, workflowRepo, executionRepo, nodeTypes) {
        return __awaiter(this, void 0, void 0, function* () {
            // Load workflow from database
            const workflowEntity = yield workflowRepo.findById(workflowId);
            if (!workflowEntity) {
                throw new Error(`Workflow with ID ${workflowId} not found in database`);
            }
            // Convert database entity to Workflow instance
            const workflow = new shared_1.Workflow({
                id: workflowEntity.id,
                name: workflowEntity.name,
                nodes: workflowEntity.nodes,
                connections: workflowEntity.connections,
                nodeTypes,
            });
            return new WorkflowExecute(workflow, workflowRepo, executionRepo);
        });
    }
    constructor(workflow, workflowRepo, executionRepo) {
        this.workflowRepo = workflowRepo;
        this.executionRepo = executionRepo;
        this.emitter = new eventemitter3_1.default();
        this.on = this.emitter.on.bind(this.emitter);
        this.once = this.emitter.once.bind(this.emitter);
        this.off = this.emitter.off.bind(this.emitter);
        this.emit = this.emitter.emit.bind(this.emitter);
        this.workflow = workflow;
        this.controller = new AbortController();
        this.multiInputNodes = new Set();
        this.waitingNodes = new Map();
        this.hooks = new execution_lifecycle_hooks_1.ExecutionLifecycleHooks(workflow.id, workflow.toJSON());
    }
    cancel() {
        this.controller.abort();
    }
    run() {
        return new p_cancelable_1.default((resolve, reject, onCancel) => {
            onCancel(() => this.controller.abort());
            this.emitter.emit('beforeWorkflowStart');
            const runAsync = () => __awaiter(this, void 0, void 0, function* () {
                let stateManager;
                try {
                    // Start execution logging if repository is available
                    if (this.executionRepo) {
                        const execution = yield this.executionRepo.startExecution(this.workflow.id);
                        this.currentExecutionId = execution.id;
                    }
                    // Create execution ID if not available
                    const executionId = this.currentExecutionId ||
                        `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                    // Create state manager
                    stateManager = new workflow_state_manager_1.WorkflowStateManager(this.workflow.id, executionId);
                    // --- START: Graph Validation ---
                    // Validate the workflow structure before execution
                    const adjacencyList = (0, graph_utils_1.buildAdjacencyList)(this.workflow.getAllNodes(), this.workflow.connections);
                    const cycle = (0, graph_utils_1.detectCycle)(adjacencyList);
                    if (cycle) {
                        // Reject the promise directly to ensure the error is propagated
                        return reject(new Error(`Workflow has a cycle: ${cycle.join(' -> ')}`));
                    }
                    // --- END: Graph Validation ---
                    // Create enhanced execution context
                    const executionContext = new execute_context_1.ExecuteContext(executionId, this.workflow.id, shared_1.WorkflowExecuteMode.MANUAL);
                    this.hooks.runHook('workflowExecuteBefore', [this.workflow.toJSON()]);
                    // Create and run graph runner
                    const graphRunner = new graph_runner_1.GraphRunner(this.workflow, stateManager, executionContext, this.hooks, this.controller.signal);
                    yield graphRunner.run();
                    // Mark execution as successful if repository is available
                    if (this.executionRepo && this.currentExecutionId) {
                        yield this.executionRepo.markAsSuccess(this.currentExecutionId);
                    }
                    // Log execution summary
                    if (stateManager) {
                        console.log('Execution Summary:', stateManager.getExecutionSummary());
                    }
                }
                catch (executionError) {
                    console.error('Workflow execution failed:', executionError);
                    // Mark execution as failed if repository is available
                    if (this.executionRepo && this.currentExecutionId) {
                        yield this.executionRepo.markAsError(this.currentExecutionId, executionError.message);
                    }
                    throw executionError;
                }
                finally {
                    this.hooks.runHook('workflowExecuteAfter', [
                        this.workflow.toJSON(),
                        (stateManager === null || stateManager === void 0 ? void 0 : stateManager.getExecutionSummary()) || {},
                    ]);
                    this.emitter.emit('afterWorkflowEnd');
                }
            });
            runAsync().then(resolve).catch(reject);
        });
    }
    applyPairedItem(inputData, outputData) {
        if (!outputData || outputData.length === 0) {
            return outputData;
        }
        if (!inputData || inputData.length === 0) {
            return outputData.map(output => (Object.assign(Object.assign({}, output), { pairedItem: { item: 0 } })));
        }
        if (inputData.length === 1) {
            return outputData.map(output => (Object.assign(Object.assign({}, output), { pairedItem: { item: 0 } })));
        }
        if (inputData.length === outputData.length) {
            return outputData.map((output, index) => (Object.assign(Object.assign({}, output), { pairedItem: { item: index } })));
        }
        return outputData.map(output => (Object.assign(Object.assign({}, output), { pairedItem: { item: 0 } })));
    }
    precomputeMultiInputNodes() {
        const allNodes = this.workflow.getAllNodes();
        const inputCounts = {};
        for (const node of allNodes) {
            inputCounts[node.id] = 0;
        }
        for (const conn of this.workflow.connections) {
            if (inputCounts[conn.targetNodeId] !== undefined) {
                inputCounts[conn.targetNodeId]++;
            }
        }
        for (const nodeId in inputCounts) {
            if (inputCounts[nodeId] > 1) {
                this.multiInputNodes.add(nodeId);
            }
        }
    }
    processNodeOutput(sourceNodeId, outputData, executionQueue) {
        const connections = this.workflow.connections.filter(c => c.sourceNodeId === sourceNodeId);
        for (const conn of connections) {
            const nextNode = this.workflow.getNode(conn.targetNodeId);
            if (!nextNode)
                continue;
            if (!this.multiInputNodes.has(nextNode.id)) {
                executionQueue.push({ node: nextNode, inputData: outputData });
                continue;
            }
            let waitingNode = this.waitingNodes.get(nextNode.id);
            if (!waitingNode) {
                waitingNode = { received: {} };
                this.waitingNodes.set(nextNode.id, waitingNode);
            }
            waitingNode.received[conn.targetInput] = outputData;
            const requiredInputs = this.workflow.connections
                .filter(c => c.targetNodeId === nextNode.id)
                .map(c => c.targetInput);
            const receivedInputs = Object.keys(waitingNode.received);
            if (requiredInputs.every(inputName => receivedInputs.includes(inputName))) {
                const combinedInputData = Object.values(waitingNode.received).flat();
                executionQueue.push({ node: nextNode, inputData: combinedInputData });
                this.waitingNodes.delete(nextNode.id);
            }
        }
    }
    execute(_mode) {
        return __awaiter(this, void 0, void 0, function* () {
            const nodes = this.workflow.getAllNodes();
            const connections = this.workflow.connections;
            const adjacencyList = (0, graph_utils_1.buildAdjacencyList)(nodes, connections);
            const cycle = (0, graph_utils_1.detectCycle)(adjacencyList);
            if (cycle) {
                throw new Error(`Workflow has a cycle: ${cycle.join(' -> ')}`);
            }
        });
    }
    executeNodeById(nodeId, inputData) {
        return __awaiter(this, void 0, void 0, function* () {
            const node = this.workflow.getNode(nodeId);
            if (!node) {
                throw new Error(`Node with ID ${nodeId} not found`);
            }
            const executionContext = new execute_context_1.ExecuteContext();
            const result = yield this.executeNode(node, inputData, executionContext);
            return [result];
        });
    }
    executeSubgraph(startNodeId, inputData) {
        return __awaiter(this, void 0, void 0, function* () {
            const executionContext = new execute_context_1.ExecuteContext();
            const executionQueue = [
                { node: this.workflow.getNode(startNodeId), inputData },
            ];
            while (executionQueue.length > 0) {
                if (this.controller.signal.aborted) {
                    throw new p_cancelable_1.CancelError('Workflow execution was canceled.');
                }
                const queueItem = executionQueue.shift();
                if (!queueItem)
                    continue;
                const { node, inputData } = queueItem;
                try {
                    this.hooks.runHook('nodeExecuteBefore', [node]);
                    const outputData = yield this.executeNode(node, inputData, executionContext);
                    this.hooks.runHook('nodeExecuteAfter', [node, outputData]);
                    const outputDataWithPairedItems = this.applyPairedItem(inputData, outputData);
                    executionContext.setNodeOutput(node.id, outputDataWithPairedItems);
                    this.processNodeOutput(node.id, outputDataWithPairedItems, executionQueue);
                }
                catch (error) {
                    if (error instanceof p_cancelable_1.CancelError) {
                        throw error;
                    }
                    this.hooks.runHook('onNodeError', [node, error]);
                    throw error;
                }
            }
        });
    }
    hasCycles() {
        const visited = new Set();
        const recursionStack = new Set();
        const hasCycleUtil = (nodeId) => {
            visited.add(nodeId);
            recursionStack.add(nodeId);
            const nextNodes = this.workflow.findNextNodes(nodeId);
            for (const nextNode of nextNodes) {
                if (!visited.has(nextNode.id)) {
                    if (hasCycleUtil(nextNode.id)) {
                        return true;
                    }
                }
                else if (recursionStack.has(nextNode.id)) {
                    return true;
                }
            }
            recursionStack.delete(nodeId);
            return false;
        };
        for (const node of this.workflow.getAllNodes()) {
            if (!visited.has(node.id)) {
                if (hasCycleUtil(node.id)) {
                    return true;
                }
            }
        }
        return false;
    }
    filterDisabledNodes() {
        return this.workflow.getAllNodes().filter(node => !node.disabled);
    }
    // New methods to access state management functionality
    runWithStateManager(runMode = shared_1.WorkflowExecuteMode.MANUAL) {
        return new p_cancelable_1.default((resolve, reject, onCancel) => {
            onCancel(() => this.controller.abort());
            const runAsync = () => __awaiter(this, void 0, void 0, function* () {
                let stateManager;
                try {
                    // Start execution logging if repository is available
                    if (this.executionRepo) {
                        const execution = yield this.executionRepo.startExecution(this.workflow.id);
                        this.currentExecutionId = execution.id;
                    }
                    // Create execution ID if not available
                    const executionId = this.currentExecutionId ||
                        `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                    // Create state manager
                    stateManager = new workflow_state_manager_1.WorkflowStateManager(this.workflow.id, executionId);
                    // Create enhanced execution context
                    const executionContext = new execute_context_1.ExecuteContext(executionId, this.workflow.id, runMode);
                    this.hooks.runHook('workflowExecuteBefore', [this.workflow.toJSON()]);
                    // Create and run graph runner
                    const graphRunner = new graph_runner_1.GraphRunner(this.workflow, stateManager, executionContext, this.hooks, this.controller.signal);
                    yield graphRunner.run();
                    // Mark execution as successful if repository is available
                    if (this.executionRepo && this.currentExecutionId) {
                        yield this.executionRepo.markAsSuccess(this.currentExecutionId);
                    }
                    return stateManager;
                }
                catch (executionError) {
                    console.error('Workflow execution failed:', executionError);
                    // Mark execution as failed if repository is available
                    if (this.executionRepo && this.currentExecutionId) {
                        yield this.executionRepo.markAsError(this.currentExecutionId, executionError.message);
                    }
                    throw executionError;
                }
                finally {
                    this.hooks.runHook('workflowExecuteAfter', [
                        this.workflow.toJSON(),
                        (stateManager === null || stateManager === void 0 ? void 0 : stateManager.getExecutionSummary()) || {},
                    ]);
                }
            });
            runAsync().then(resolve).catch(reject);
        });
    }
    // Method to get a fresh NodeExecutor instance
    createNodeExecutor(stateManager, abortSignal) {
        return new node_executor_1.NodeExecutor(this.workflow, stateManager, abortSignal || this.controller.signal);
    }
    // Method to create a fresh execution context
    createExecutionContext(executionId, runMode = shared_1.WorkflowExecuteMode.MANUAL) {
        const execId = executionId || `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        return new execute_context_1.ExecuteContext(execId, this.workflow.id, runMode);
    }
    // Method to create a fresh GraphRunner
    createGraphRunner(stateManager, context, abortSignal) {
        return new graph_runner_1.GraphRunner(this.workflow, stateManager, context, this.hooks, abortSignal || this.controller.signal);
    }
    // Legacy method kept for backwards compatibility
    executeNode(node, inputData, context) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            if (this.controller.signal.aborted) {
                throw new p_cancelable_1.CancelError('Workflow execution was canceled.');
            }
            const nodeType = (_a = this.workflow.nodeTypes) === null || _a === void 0 ? void 0 : _a.getByNameAndVersion(node.type, node.typeVersion || 1);
            if (!nodeType) {
                throw new Error(`Node type "${node.type}" not found.`);
            }
            const nodeContext = new workflow_node_context_1.WorkflowNodeContext(this.workflow, node, context.getAll());
            const executeFunctions = new node_execution_functions_1.NodeExecuteFunctions(nodeContext, node, inputData);
            const result = yield nodeType.execute.call(executeFunctions);
            if (Array.isArray(result) && Array.isArray(result[0])) {
                return result.flat();
            }
            return result;
        });
    }
}
exports.WorkflowExecute = WorkflowExecute;
//# sourceMappingURL=workflow-execute.js.map