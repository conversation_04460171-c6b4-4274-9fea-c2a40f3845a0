import { DataSource, Repository } from 'typeorm';
import { ProxyEntity } from '../entities/proxy.entity';
export interface ProxySearchCriteria {
    type?: 'http' | 'https' | 'socks4' | 'socks5';
    country?: string;
    city?: string;
    enabled?: boolean;
    minHealthScore?: number;
    isBlacklisted?: boolean;
}
export declare class ProxyRepository extends Repository<ProxyEntity> {
    constructor(dataSource: DataSource);
    /**
     * Find entity by ID - added for compatibility with core package
     */
    findById(id: string): Promise<ProxyEntity | null>;
    /**
     * Create a new proxy entity and return it
     */
    createProxy(proxyData: Partial<ProxyEntity>): Promise<ProxyEntity>;
    /**
     * Update proxy and return the updated entity
     */
    updateProxy(id: string, updateData: Partial<ProxyEntity>): Promise<ProxyEntity | null>;
    /**
     * Delete proxy by id and return success status
     */
    deleteProxy(id: string): Promise<boolean>;
    /**
     * Find all proxies
     */
    findAll(): Promise<ProxyEntity[]>;
    /**
     * Find enabled proxies
     */
    findEnabled(): Promise<ProxyEntity[]>;
    /**
     * Find proxies by type
     */
    findByType(type: 'http' | 'https' | 'socks4' | 'socks5'): Promise<ProxyEntity[]>;
    /**
     * Find proxies by country
     */
    findByCountry(country: string): Promise<ProxyEntity[]>;
    /**
     * Find proxies by host
     */
    findByHost(host: string): Promise<ProxyEntity[]>;
    /**
     * Find healthy proxies with minimum health score
     */
    findHealthy(minHealthScore?: number): Promise<ProxyEntity[]>;
    /**
     * Find non-blacklisted proxies
     */
    findNonBlacklisted(): Promise<ProxyEntity[]>;
    /**
     * Search proxies with multiple criteria
     */
    search(criteria: ProxySearchCriteria): Promise<ProxyEntity[]>;
    /**
     * Get the best available proxy based on health score and blacklist status
     */
    getBestAvailable(type?: 'http' | 'https' | 'socks4' | 'socks5'): Promise<ProxyEntity | null>;
    /**
     * Update health score
     */
    updateHealthScore(id: string, healthScore: number): Promise<void>;
    /**
     * Blacklist a proxy until a specific date
     */
    blacklist(id: string, until: Date): Promise<void>;
    /**
     * Remove blacklist from a proxy
     */
    removeBlacklist(id: string): Promise<void>;
    /**
     * Find proxies with their associated profiles
     */
    findWithProfiles(id?: string): Promise<ProxyEntity | ProxyEntity[]>;
    /**
     * Get proxy statistics
     */
    getStats(): Promise<{
        total: number;
        enabled: number;
        disabled: number;
        healthy: number;
        unhealthy: number;
        blacklisted: number;
        byType: Record<string, number>;
        byCountry: Record<string, number>;
    }>;
}
//# sourceMappingURL=proxy.repository.d.ts.map