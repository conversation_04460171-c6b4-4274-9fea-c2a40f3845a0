"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecuteContext = void 0;
const shared_1 = require("@gygalogin/shared");
const base_execute_context_1 = require("./base-execute-context");
class ExecuteContext extends base_execute_context_1.BaseExecuteContext {
    constructor(executionId, workflowId, runMode, initialContext) {
        super(initialContext);
        this.data = Object.assign(Object.assign({}, this.data), { executionId,
            workflowId, runMode: runMode || shared_1.WorkflowExecuteMode.MANUAL, sharedData: {} });
    }
    setNodeOutput(nodeId, data) {
        this.data.nodeOutputs[nodeId] = data;
    }
    getNodeOutput(nodeId) {
        return this.data.nodeOutputs[nodeId];
    }
    getExecutionId() {
        return this.data.executionId;
    }
    getWorkflowId() {
        return this.data.workflowId;
    }
    getRunMode() {
        return this.data.runMode;
    }
    setSharedData(key, value) {
        this.data.sharedData[key] = value;
    }
    getSharedData(key) {
        if (key) {
            return this.data.sharedData[key];
        }
        return this.data.sharedData;
    }
    getEnhancedContext() {
        return Object.assign({}, this.data);
    }
}
exports.ExecuteContext = ExecuteContext;
//# sourceMappingURL=execute-context.js.map