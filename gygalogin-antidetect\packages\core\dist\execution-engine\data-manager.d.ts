import { INodeExecutionData, IDataObject } from '@gygalogin/shared';
export interface IPairedItemOptions {
    mode?: 'default' | 'oneToOne' | 'manyToOne' | 'oneToMany';
    sourceItemIndex?: number;
}
export interface IDataMergeOptions {
    mergeBy?: string;
    strategy?: 'merge' | 'replace' | 'append';
    preserveKeys?: string[];
}
export interface IDataSplitOptions {
    splitBy?: string;
    maxItems?: number;
    preserveStructure?: boolean;
}
export interface IDataTransformOptions {
    transform?: (item: IDataObject, index: number) => IDataObject;
    filter?: (item: IDataObject, index: number) => boolean;
    sort?: (a: IDataObject, b: IDataObject) => number;
}
export declare class DataManager {
    /**
     * Apply paired items to output data (improved version from NodeExecutor)
     */
    static applyPairedItem(inputData: INodeExecutionData[], outputData: INodeExecutionData[], options?: IPairedItemOptions): INodeExecutionData[];
    /**
     * Default pairing logic (from original implementation)
     */
    private static applyDefaultPairing;
    /**
     * One-to-one pairing: each output item is paired with corresponding input item
     */
    private static applyOneToOnePairing;
    /**
     * Many-to-one pairing: all output items are paired with one specific input item
     */
    private static applyManyToOnePairing;
    /**
     * One-to-many pairing: output items are distributed across input items
     */
    private static applyOneToManyPairing;
    /**
     * Merge multiple data arrays based on a key or strategy
     */
    static mergeData(dataArrays: INodeExecutionData[][], options?: IDataMergeOptions): INodeExecutionData[];
    /**
     * Append all data arrays together
     */
    private static appendStrategy;
    /**
     * Use the last non-empty array (replace strategy)
     */
    private static replaceStrategy;
    /**
     * Merge arrays by a specific key
     */
    private static mergeByKey;
    /**
     * Split data array based on criteria
     */
    static splitData(data: INodeExecutionData[], options?: IDataSplitOptions): INodeExecutionData[][];
    /**
     * Split by maximum number of items per group
     */
    private static splitByCount;
    /**
     * Split by a key value
     */
    private static splitByKey;
    /**
     * Transform data with filtering, transforming, and sorting
     */
    static transformData(data: INodeExecutionData[], options?: IDataTransformOptions): INodeExecutionData[];
    /**
     * Get data from specific input by name (for multi-input nodes)
     */
    static getInputDataFrom(allNodeOutputs: {
        [nodeId: string]: INodeExecutionData[];
    }, nodeName: string): INodeExecutionData[];
    /**
     * Get specific item by index from data array
     */
    static getInputDataByIndex(data: INodeExecutionData[], itemIndex: number): INodeExecutionData | undefined;
    /**
     * Validate data structure
     */
    static validateData(data: INodeExecutionData[]): {
        isValid: boolean;
        errors: string[];
    };
    /**
     * Create empty data item
     */
    static createEmptyItem(): INodeExecutionData;
    /**
     * Clone data array deeply
     */
    static cloneData(data: INodeExecutionData[]): INodeExecutionData[];
}
//# sourceMappingURL=data-manager.d.ts.map