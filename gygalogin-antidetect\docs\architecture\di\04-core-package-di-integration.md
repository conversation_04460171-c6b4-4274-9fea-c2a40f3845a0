# Core Package Dependency Injection Integration

## Overview

This document outlines the integration of the `@gygalogin/decorators` package with the existing `@gygalogin/core` package to enable dependency injection for core services, following n8n's proven patterns for execution engine, browser management, and workflow services.

**Key Goals:**

1. Add DI support to existing core services without breaking changes
2. Enable automatic service injection across packages
3. Maintain current functionality and performance
4. Follow n8n's core service DI architecture
5. Prepare for seamless integration with CLI, API, and DB layers

## Current Core Package Analysis

### Existing Structure

```
packages/core/src/
├── browser/                     # Browser automation services
│   ├── BrowserManager.ts        # Browser lifecycle management
│   ├── PageController.ts        # Page interaction control
│   └── index.ts
├── collectors/                  # Data collection services
│   ├── BrowserDataCollectorManager.ts
│   ├── CacheStorageCollector.ts
│   ├── IndexedDBCollector.ts
│   └── WebSQLCollector.ts
├── execution-engine/            # Workflow execution engine
│   ├── active-workflows.ts
│   ├── enhanced-node-executor.ts
│   ├── graph-runner.ts
│   ├── scheduled-task-manager.ts
│   ├── workflow-execute.ts
│   └── workflow-state-manager.ts
├── services/                    # Core services
│   └── credential.service.ts    # Credential management
├── devtools/                    # DevTools integration
├── reporting/                   # Execution reporting
├── errors/                      # Error definitions
└── events.ts                    # Event system
```

### Current Service Pattern

```typescript
// Current approach (manual instantiation)
export class BrowserManager {
  constructor(logger?: Logger) {
    this.logger = logger || new Logger({ service: 'BrowserManager' });
  }
}

// Usage requires manual setup
const logger = new Logger();
const browserManager = new BrowserManager(logger);
```

## Target Architecture (n8n Pattern)

### Enhanced Structure with DI

```
packages/core/src/
├── browser/                     # DI-enabled browser services
│   ├── browser-manager.service.ts    # @Service() decorated
│   ├── page-controller.service.ts    # @Service() decorated
│   └── index.ts
├── collectors/                  # DI-enabled data collectors
│   ├── browser-data-collector.service.ts
│   ├── cache-storage-collector.service.ts
│   └── index.ts
├── execution-engine/            # DI-enabled execution services
│   ├── workflow-executor.factory.ts    # Factory for isolated execution
│   ├── workflow-executor.ts            # Non-singleton executor
│   ├── graph-runner.factory.ts         # Factory for graph traversal
│   ├── graph-runner.ts                 # Non-singleton runner
│   ├── workflow-state-manager.factory.ts # Factory for state management
│   ├── workflow-state-manager.ts       # Non-singleton state manager
│   ├── scheduled-task-manager.service.ts # Singleton for global cron jobs
│   └── index.ts
├── services/                    # Enhanced core services
│   ├── credential.service.ts    # @Service() decorated
│   ├── config.service.ts        # NEW: Configuration management
│   ├── logger.service.ts        # NEW: Centralized logging
│   └── index.ts
├── devtools/                    # DI-enabled DevTools
├── reporting/                   # DI-enabled reporting
├── errors/                      # Error definitions (unchanged)
└── events.ts                    # Enhanced event system
```

### Target Service Pattern (Multi-Instance Safe)

```typescript
// Target approach (DI-enabled with multi-instance support)
@Service()
export class BrowserManagerFactory {
  constructor(
    private readonly logger: Logger,
    private readonly configService: ConfigService
  ) {}

  // Create isolated browser manager instances
  createBrowserManager(managerId?: string): BrowserManager {
    return new BrowserManager(
      this.logger.scoped(`BrowserManager:${managerId || 'default'}`),
      this.configService
    );
  }
}

// Enhanced BrowserManager (non-singleton, thread-safe)
export class BrowserManager {
  private browsers: Map<string, BrowserInstance> = new Map();
  private readonly managerId: string;

  constructor(
    private readonly logger: Logger,
    private readonly configService: ConfigService,
    managerId?: string
  ) {
    this.managerId = managerId || `mgr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // All existing methods remain the same, but now isolated per instance
  async launchBrowser(profile: Profile): Promise<string> {
    // Implementation unchanged - each manager handles its own browsers
  }
}

// Usage with DI (automatic injection with isolation)
@Service()
export class ProfileService {
  private browserManager: BrowserManager;

  constructor(private readonly browserFactory: BrowserManagerFactory) {
    // Each service gets its own isolated browser manager
    this.browserManager = browserFactory.createBrowserManager('profile-service');
  }

  async launchProfileBrowser(profileId: string) {
    return await this.browserManager.launchBrowser(profile);
  }
}
```

## Factory vs Singleton Decision Matrix

### **🏭 Services That Need Factory Pattern (Non-Singleton)**

| Service                  | Reason                                | State Management                                 |
| ------------------------ | ------------------------------------- | ------------------------------------------------ |
| **BrowserManager**       | Manages browser instances per context | Each manager handles its own browser pool        |
| **WorkflowExecutor**     | Manages execution state per workflow  | Isolated execution contexts prevent interference |
| **GraphRunner**          | Manages graph traversal per execution | Each execution needs independent graph state     |
| **WorkflowStateManager** | Manages state per workflow execution  | Execution state must be isolated                 |
| **NodeExecutor**         | Manages node execution per workflow   | Node execution state per workflow                |
| **DevToolsListener\***   | Manages DevTools per browser instance | Per-browser DevTools attachment                  |

\*DevToolsListener uses factory if attached per-browser, singleton if global monitoring

### **🔄 Services That Should Remain Singleton**

| Service                  | Reason                           | Shared Benefits                           |
| ------------------------ | -------------------------------- | ----------------------------------------- |
| **ScheduledTaskManager** | Global cron job management       | Centralized scheduling, no duplicate jobs |
| **CredentialService**    | Shared credential cache          | Efficient caching, consistent credentials |
| **ConfigService**        | Global application configuration | Consistent config across all services     |
| **DataCollectors**       | Shared data aggregation          | Centralized data collection and reporting |

### **🎯 Architecture Benefits**

```typescript
// Factory Pattern - Isolated Execution
const executor1 = executorFactory.createWorkflowExecutor('workflow-1');
const executor2 = executorFactory.createWorkflowExecutor('workflow-2');

// Each executor has completely isolated state
executor1.execute(); // Independent execution
executor2.execute(); // Independent execution

// Singleton Pattern - Shared Resources
const credentialService = Container.get(CredentialService); // Same instance everywhere
const configService = Container.get(ConfigService); // Same instance everywhere
```

## Implementation Tasks

### Phase 1: Add DI Dependencies (Priority: High)

#### Task 1.1: Update Package Dependencies

```json
// packages/core/package.json
{
  "name": "@gygalogin/core",
  "dependencies": {
    "@gygalogin/decorators": "workspace:*",
    "@gygalogin/db": "workspace:*",
    "@gygalogin/node-base": "workspace:*",
    "@gygalogin/shared": "workspace:*",
    "reflect-metadata": "^0.1.13",
    "eventemitter3": "^5.0.1",
    "idb": "^7.1.1",
    "node-cron": "^3.0.3",
    "p-cancelable": "^4.0.1"
  }
}
```

#### Task 1.2: Update TypeScript Configuration

```json
// packages/core/tsconfig.json
{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "outDir": "./dist",
    "rootDir": "./src"
  }
}
```

### Phase 2: Core Service Layer (Priority: High)

#### Task 2.1: Create Configuration Service

```typescript
// src/services/config.service.ts
import { Service } from '@gygalogin/decorators/di';
import { Logger } from '@gygalogin/shared';

export interface CoreConfig {
  browser: {
    headless: boolean;
    timeout: number;
    maxInstances: number;
    chromePath?: string;
  };
  execution: {
    maxConcurrentWorkflows: number;
    timeoutMs: number;
    retryAttempts: number;
  };
  logging: {
    level: string;
    format: string;
  };
  devtools: {
    enabled: boolean;
    port?: number;
  };
}

@Service()
export class ConfigService {
  private config: CoreConfig;

  constructor(private readonly logger: Logger) {
    this.loadConfig();
  }

  private loadConfig(): void {
    this.config = {
      browser: {
        headless: process.env.BROWSER_HEADLESS === 'true',
        timeout: parseInt(process.env.BROWSER_TIMEOUT || '30000'),
        maxInstances: parseInt(process.env.BROWSER_MAX_INSTANCES || '5'),
        chromePath: process.env.CHROME_PATH,
      },
      execution: {
        maxConcurrentWorkflows: parseInt(process.env.MAX_CONCURRENT_WORKFLOWS || '10'),
        timeoutMs: parseInt(process.env.EXECUTION_TIMEOUT || '300000'),
        retryAttempts: parseInt(process.env.RETRY_ATTEMPTS || '3'),
      },
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'json',
      },
      devtools: {
        enabled: process.env.DEVTOOLS_ENABLED === 'true',
        port: process.env.DEVTOOLS_PORT ? parseInt(process.env.DEVTOOLS_PORT) : undefined,
      },
    };

    this.logger.info('✅ Core configuration loaded', { config: this.config });
  }

  get browser() {
    return this.config.browser;
  }

  get execution() {
    return this.config.execution;
  }

  get logging() {
    return this.config.logging;
  }

  get devtools() {
    return this.config.devtools;
  }

  updateConfig(updates: Partial<CoreConfig>): void {
    this.config = { ...this.config, ...updates };
    this.logger.info('🔄 Core configuration updated', { updates });
  }
}
```

#### Task 2.2: Enhance Credential Service with DI

```typescript
// src/services/credential.service.ts (Enhanced)
import { Service } from '@gygalogin/decorators/di';
import { Logger } from '@gygalogin/shared';

// Keep existing interfaces...

@Service()
export class CredentialService {
  private repository?: ICredentialRepository;
  private encryption?: ICredentialEncryption;
  private cache: Map<string, { data: ICredentialDataDecryptedObject; expires: number }> = new Map();
  private cacheEnabled: boolean;
  private cacheTTL: number;

  constructor(
    private readonly logger: Logger,
    options: ICredentialServiceOptions = {}
  ) {
    this.repository = options.repository;
    this.encryption = options.encryption;
    this.cacheEnabled = options.cacheEnabled ?? true;
    this.cacheTTL = options.cacheTTL ?? 300000; // 5 minutes default

    this.logger.info('✅ CredentialService initialized');
  }

  // All existing methods remain the same...
  // Just add logging where appropriate
}
```

### Phase 3: Browser Services Migration (Priority: High)

#### Task 3.1: Create Browser Manager Factory

```typescript
// src/browser/browser-manager.factory.ts (NEW - DI Factory)
import { Service } from '@gygalogin/decorators/di';
import { Logger } from '@gygalogin/shared';
import { ConfigService } from '../services/config.service';
import { BrowserManager } from './browser-manager';

@Service()
export class BrowserManagerFactory {
  constructor(
    private readonly logger: Logger,
    private readonly configService: ConfigService
  ) {}

  /**
   * Create a new isolated BrowserManager instance
   * Each instance manages its own set of browsers independently
   */
  createBrowserManager(managerId?: string): BrowserManager {
    const scopedLogger = this.logger.scoped(`BrowserManager:${managerId || 'default'}`);
    return new BrowserManager(scopedLogger, this.configService, managerId);
  }

  /**
   * Create a browser manager for workflow execution
   * Optimized for concurrent workflow execution
   */
  createWorkflowBrowserManager(workflowId: string): BrowserManager {
    return this.createBrowserManager(`workflow-${workflowId}`);
  }

  /**
   * Create a browser manager for CLI operations
   * Optimized for interactive CLI usage
   */
  createCliBrowserManager(): BrowserManager {
    return this.createBrowserManager('cli');
  }
}
```

#### Task 3.2: Enhance Existing Browser Manager

```typescript
// src/browser/browser-manager.ts (Enhanced - keep existing functionality)
import { Logger } from '@gygalogin/shared';
import { ConfigService } from '../services/config.service';
import puppeteer, { Browser, Page, LaunchOptions } from 'puppeteer';
import { addExtra } from 'puppeteer-extra';
import StealthPlugin = require('puppeteer-extra-plugin-stealth');
import { v4 as uuidv4 } from 'uuid';
import { Profile, BrowserError, DEFAULT_CONFIG } from '@gygalogin/shared';
import { DevToolsListener } from '../devtools/DevToolsListener';

// Keep all existing interfaces...
export interface BrowserInstance {
  id: string;
  browser: Browser;
  pages: Map<string, Page>;
  profile: Profile;
  createdAt: Date;
  lastActivity: Date;
  devToolsListener?: DevToolsListener;
}

export interface RetryOptions {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
}

export class BrowserManager {
  private browsers: Map<string, BrowserInstance> = new Map();
  private chromePath?: string;
  private puppeteerExtra = addExtra(puppeteer);
  private readonly managerId: string;
  private defaultRetryOptions: RetryOptions;

  constructor(
    private readonly logger: Logger,
    private readonly configService?: ConfigService,
    managerId?: string
  ) {
    this.managerId = managerId || `mgr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.puppeteerExtra.use(StealthPlugin());

    // Use config service if available, otherwise use defaults
    this.defaultRetryOptions = this.configService?.browser
      ? {
          maxRetries: 3,
          baseDelay: 1000,
          maxDelay: 5000,
        }
      : {
          maxRetries: 3,
          baseDelay: 1000,
          maxDelay: 5000,
        };

    this.initializeChromePath();

    this.logger.info('BrowserManager initialized', {
      managerId: this.managerId,
      hasConfig: !!this.configService,
    });
  }

  // ALL EXISTING METHODS REMAIN UNCHANGED - just enhanced with DI logging:
  // - async launchBrowser(profile: Profile): Promise<string>
  // - getBrowser(instanceId: string): BrowserInstance | null
  // - async createPage(instanceId: string): Promise<string>
  // - getPage(instanceId: string, pageId: string): Page | null
  // - async navigate(instanceId: string, pageId: string, url: string): Promise<void>
  // - async closePage(instanceId: string, pageId: string): Promise<void>
  // - async closeBrowser(instanceId: string): Promise<void>
  // - getActiveBrowsers(): BrowserInstance[]
  // - async cleanupInactiveBrowsers(maxIdleTime?: number): Promise<void>
  // - async closeAllBrowsers(): Promise<void>
  // - private async prepareUserDataDir(profileId: string): Promise<string>
  // - private getProxyArgs(proxy): string[]
  // - private async applyFingerprint(page: Page, profile: Profile): Promise<void>
  // - private validateProfile(profile: Profile): void
  // - private async retry<T>(operation, options, context): Promise<T>
  // - private initializeChromePath(): void
  // - private detectChromePath(): string | undefined

  /**
   * Get manager statistics for monitoring
   * NEW method for multi-instance management
   */
  getManagerStats(): {
    managerId: string;
    activeBrowsers: number;
    totalPages: number;
    oldestBrowser?: Date;
    newestBrowser?: Date;
  } {
    const browsers = Array.from(this.browsers.values());
    const totalPages = browsers.reduce((sum, browser) => sum + browser.pages.size, 0);

    return {
      managerId: this.managerId,
      activeBrowsers: browsers.length,
      totalPages,
      oldestBrowser:
        browsers.length > 0
          ? new Date(Math.min(...browsers.map(b => b.createdAt.getTime())))
          : undefined,
      newestBrowser:
        browsers.length > 0
          ? new Date(Math.max(...browsers.map(b => b.createdAt.getTime())))
          : undefined,
    };
  }

  /**
   * Close a browser instance
   */
  async closeBrowser(instanceId: string): Promise<void> {
    const instance = this.browsers.get(instanceId);
    if (!instance) {
      throw new BrowserError(`Browser instance ${instanceId} not found`);
    }

    this.logger.info('🔌 Closing browser', { instanceId });

    try {
      await instance.browser.close();
      this.browsers.delete(instanceId);

      this.logger.info('✅ Browser closed successfully', { instanceId });
    } catch (error) {
      this.logger.error('❌ Failed to close browser', {
        instanceId,
        error: error.message,
      });
      throw new BrowserError(`Failed to close browser: ${error.message}`);
    }
  }

  /**
   * Get all active browser instances
   */
  getActiveBrowsers(): BrowserInstance[] {
    return Array.from(this.browsers.values());
  }

  /**
   * Get browser instance by ID
   */
  getBrowserInstance(instanceId: string): BrowserInstance | undefined {
    return this.browsers.get(instanceId);
  }

  /**
   * Check if maximum browser instances reached
   */
  isMaxInstancesReached(): boolean {
    const maxInstances = this.configService.browser.maxInstances;
    return this.browsers.size >= maxInstances;
  }

  // Private methods remain the same...
  private initializeChromePath(): void {
    // Implementation unchanged
  }

  private buildLaunchArgs(profile: Profile): string[] {
    // Implementation unchanged
  }
}
```

#### Task 3.2: Migrate Page Controller Service

```typescript
// src/browser/page-controller.service.ts (Enhanced with DI)
import { Service } from '@gygalogin/decorators/di';
import { Logger } from '@gygalogin/shared';
import { BrowserManagerService } from './browser-manager.service';
import { Page } from 'puppeteer';

@Service()
export class PageControllerService {
  constructor(
    private readonly logger: Logger,
    private readonly browserManager: BrowserManagerService
  ) {
    this.logger = this.logger.scoped('PageController');
  }

  /**
   * Create a new page in the browser instance
   */
  async createPage(instanceId: string): Promise<Page> {
    const instance = this.browserManager.getBrowserInstance(instanceId);
    if (!instance) {
      throw new Error(`Browser instance ${instanceId} not found`);
    }

    this.logger.info('📄 Creating new page', { instanceId });

    try {
      const page = await instance.browser.newPage();
      const pageId = `page_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      instance.pages.set(pageId, page);
      instance.lastActivity = new Date();

      this.logger.info('✅ Page created successfully', { instanceId, pageId });

      return page;
    } catch (error) {
      this.logger.error('❌ Failed to create page', {
        instanceId,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Navigate to URL
   */
  async navigateToUrl(page: Page, url: string): Promise<void> {
    this.logger.info('🌐 Navigating to URL', { url });

    try {
      await page.goto(url, { waitUntil: 'networkidle2' });
      this.logger.info('✅ Navigation successful', { url });
    } catch (error) {
      this.logger.error('❌ Navigation failed', { url, error: error.message });
      throw error;
    }
  }

  // Additional page control methods...
}
```

### Phase 4: Execution Engine Migration (Priority: High)

#### Task 4.1: Create Workflow Executor Factory

```typescript
// src/execution-engine/workflow-executor.factory.ts (NEW - DI Factory)
import { Service } from '@gygalogin/decorators/di';
import { Logger } from '@gygalogin/shared';
import { ConfigService } from '../services/config.service';
import { BrowserManagerFactory } from '../browser/browser-manager.factory';
import { CredentialService } from '../services/credential.service';
import { WorkflowExecutor } from './workflow-executor';

@Service()
export class WorkflowExecutorFactory {
  constructor(
    private readonly logger: Logger,
    private readonly configService: ConfigService,
    private readonly browserManagerFactory: BrowserManagerFactory,
    private readonly credentialService: CredentialService
  ) {}

  /**
   * Create a new isolated WorkflowExecutor instance
   * Each execution gets its own executor with isolated state
   */
  createWorkflowExecutor(executionId: string): WorkflowExecutor {
    const scopedLogger = this.logger.scoped(`WorkflowExecutor:${executionId}`);
    return new WorkflowExecutor(
      scopedLogger,
      this.configService,
      this.browserManagerFactory,
      this.credentialService,
      executionId
    );
  }

  /**
   * Create executor for CLI-initiated workflows
   */
  createCliWorkflowExecutor(executionId: string): WorkflowExecutor {
    return this.createWorkflowExecutor(`cli-${executionId}`);
  }

  /**
   * Create executor for API-initiated workflows
   */
  createApiWorkflowExecutor(executionId: string): WorkflowExecutor {
    return this.createWorkflowExecutor(`api-${executionId}`);
  }
}
```

#### Task 4.2: Enhance Workflow Executor (Non-Singleton)

```typescript
// src/execution-engine/workflow-executor.ts (Enhanced - keep existing functionality)
import { Logger } from '@gygalogin/shared';
import { ConfigService } from '../services/config.service';
import { BrowserManagerFactory } from '../browser/browser-manager.factory';
import { CredentialService } from '../services/credential.service';
import { BrowserManager } from '../browser/browser-manager';

export interface WorkflowExecutionContext {
  workflowId: string;
  executionId: string;
  profileId?: string;
  credentials?: Record<string, any>;
  variables?: Record<string, any>;
}

export interface WorkflowExecutionResult {
  success: boolean;
  executionId: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  error?: string;
  data?: any;
}

export class WorkflowExecutor {
  private executionState = new Map<string, any>();
  private browserManager: BrowserManager;
  private readonly executionId: string;

  constructor(
    private readonly logger: Logger,
    private readonly configService: ConfigService,
    private readonly browserManagerFactory: BrowserManagerFactory,
    private readonly credentialService: CredentialService,
    executionId: string
  ) {
    this.executionId = executionId;
    // Each executor gets its own isolated browser manager
    this.browserManager = browserManagerFactory.createBrowserManager(`exec-${executionId}`);

    this.logger.info('WorkflowExecutor initialized', {
      executionId: this.executionId,
      browserManagerId: this.browserManager.getManagerStats().managerId,
    });
  }

  async executeWorkflow(context: WorkflowExecutionContext): Promise<WorkflowExecutionResult> {
    const startTime = new Date();

    this.logger.info('🚀 Starting workflow execution', {
      workflowId: context.workflowId,
      executionId: context.executionId,
      profileId: context.profileId,
    });

    try {
      // Load credentials if needed
      if (context.credentials) {
        for (const [key, credentialName] of Object.entries(context.credentials)) {
          context.credentials[key] = await this.credentialService.getCredentials(credentialName);
        }
      }

      // Execute workflow with isolated browser manager
      const result = await this.performWorkflowExecution(context);

      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      this.logger.info('✅ Workflow execution completed', {
        workflowId: context.workflowId,
        executionId: context.executionId,
        duration,
      });

      return {
        success: true,
        executionId: context.executionId,
        startTime,
        endTime,
        duration,
        data: result,
      };
    } catch (error) {
      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      this.logger.error('❌ Workflow execution failed', {
        workflowId: context.workflowId,
        executionId: context.executionId,
        duration,
        error: error.message,
      });

      return {
        success: false,
        executionId: context.executionId,
        startTime,
        endTime,
        duration,
        error: error.message,
      };
    }
  }

  private async performWorkflowExecution(context: WorkflowExecutionContext): Promise<any> {
    // Integration with existing WorkflowExecute class
    // This would use your existing workflow execution logic
    return { message: 'Workflow executed successfully' };
  }

  getBrowserManager(): BrowserManager {
    return this.browserManager;
  }

  getExecutionStats() {
    return {
      executionId: this.executionId,
      browserStats: this.browserManager.getManagerStats(),
      executionState: this.executionState.size,
    };
  }

  async cleanup(): Promise<void> {
    this.logger.info('🧹 Cleaning up workflow executor', { executionId: this.executionId });

    // Cleanup browsers
    await this.browserManager.closeAllBrowsers();

    // Clear execution state
    this.executionState.clear();

    this.logger.info('✅ Workflow executor cleanup complete', { executionId: this.executionId });
  }
}
```

#### Task 4.3: Migrate Scheduled Task Manager Service (Singleton)

```typescript
// src/execution-engine/scheduled-task-manager.service.ts (Enhanced with DI)
import { Service } from '@gygalogin/decorators/di';
import { Logger } from '@gygalogin/shared';
import { ConfigService } from '../services/config.service';
import { CronJob } from 'cron';

@Service()
export class ScheduledTaskManagerService {
  readonly cronMap = new Map<string, Array<{ job: CronJob; displayableCron: string }>>();
  private readonly logInterval?: NodeJS.Timeout;

  constructor(
    private readonly logger: Logger,
    private readonly configService: ConfigService
  ) {
    this.logger = this.logger.scoped('ScheduledTaskManager');

    // Initialize logging interval if configured
    const activeInterval = this.configService.execution.retryAttempts; // Reuse config
    if (activeInterval > 0) {
      this.logInterval = setInterval(
        () => this.logActiveCrons(),
        activeInterval * 60000 // Convert to milliseconds
      );
    }
  }

  private logActiveCrons() {
    const activeCrons: Record<string, string[]> = {};
    for (const [workflowId, cronJobs] of this.cronMap) {
      activeCrons[`workflow-${workflowId}`] = cronJobs.map(
        ({ displayableCron }) => displayableCron
      );
    }

    if (Object.keys(activeCrons).length === 0) return;

    this.logger.debug('Currently active crons', { activeCrons });
  }

  registerCron(workflowId: string, expression: string, onTick: () => void) {
    this.logger.info('📅 Registering cron job', { workflowId, expression });

    try {
      const job = new CronJob(expression, onTick, null, false);

      if (!this.cronMap.has(workflowId)) {
        this.cronMap.set(workflowId, []);
      }

      this.cronMap.get(workflowId)!.push({
        job,
        displayableCron: expression,
      });

      job.start();

      this.logger.info('✅ Cron job registered and started', { workflowId, expression });
    } catch (error) {
      this.logger.error('❌ Failed to register cron job', {
        workflowId,
        expression,
        error: error.message,
      });
      throw error;
    }
  }

  unregisterCron(workflowId: string) {
    const cronJobs = this.cronMap.get(workflowId);
    if (!cronJobs) return;

    this.logger.info('🗑️ Unregistering cron jobs', { workflowId, count: cronJobs.length });

    cronJobs.forEach(({ job }) => {
      job.stop();
      job.destroy();
    });

    this.cronMap.delete(workflowId);

    this.logger.info('✅ Cron jobs unregistered', { workflowId });
  }

  shutdown() {
    this.logger.info('🔌 Shutting down scheduled task manager');

    if (this.logInterval) {
      clearInterval(this.logInterval);
    }

    for (const [workflowId] of this.cronMap) {
      this.unregisterCron(workflowId);
    }

    this.logger.info('✅ Scheduled task manager shutdown complete');
  }
}
```

#### Task 4.2: Migrate Workflow Executor Service

```typescript
// src/execution-engine/workflow-executor.service.ts (Enhanced with DI)
import { Service } from '@gygalogin/decorators/di';
import { Logger } from '@gygalogin/shared';
import { ConfigService } from '../services/config.service';
import { BrowserManagerService } from '../browser/browser-manager.service';
import { CredentialService } from '../services/credential.service';

export interface WorkflowExecutionContext {
  workflowId: string;
  executionId: string;
  profileId?: string;
  credentials?: Record<string, any>;
  variables?: Record<string, any>;
}

export interface WorkflowExecutionResult {
  success: boolean;
  executionId: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  error?: string;
  data?: any;
}

@Service()
export class WorkflowExecutorService {
  private activeExecutions = new Map<string, WorkflowExecutionContext>();
  private browserManagers = new Map<string, BrowserManager>();

  constructor(
    private readonly logger: Logger,
    private readonly configService: ConfigService,
    private readonly browserManagerFactory: BrowserManagerFactory,
    private readonly credentialService: CredentialService
  ) {
    this.logger = this.logger.scoped('WorkflowExecutor');
  }

  /**
   * Get or create a browser manager for a specific workflow
   * Each workflow gets its own isolated browser manager
   */
  private getBrowserManagerForWorkflow(workflowId: string): BrowserManager {
    if (!this.browserManagers.has(workflowId)) {
      const browserManager = this.browserManagerFactory.createWorkflowBrowserManager(workflowId);
      this.browserManagers.set(workflowId, browserManager);

      this.logger.info('Created browser manager for workflow', {
        workflowId,
        managerId: browserManager.getManagerStats().managerId,
      });
    }

    return this.browserManagers.get(workflowId)!;
  }

  async executeWorkflow(context: WorkflowExecutionContext): Promise<WorkflowExecutionResult> {
    const startTime = new Date();

    this.logger.info('🚀 Starting workflow execution', {
      workflowId: context.workflowId,
      executionId: context.executionId,
      profileId: context.profileId,
    });

    this.activeExecutions.set(context.executionId, context);

    try {
      // Check concurrent execution limits
      const maxConcurrent = this.configService.execution.maxConcurrentWorkflows;
      if (this.activeExecutions.size > maxConcurrent) {
        throw new Error(`Maximum concurrent executions (${maxConcurrent}) exceeded`);
      }

      // Load credentials if needed
      if (context.credentials) {
        for (const [key, credentialName] of Object.entries(context.credentials)) {
          context.credentials[key] = await this.credentialService.getCredentials(credentialName);
        }
      }

      // Execute workflow logic here...
      const result = await this.performWorkflowExecution(context);

      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      this.logger.info('✅ Workflow execution completed', {
        workflowId: context.workflowId,
        executionId: context.executionId,
        duration,
      });

      return {
        success: true,
        executionId: context.executionId,
        startTime,
        endTime,
        duration,
        data: result,
      };
    } catch (error) {
      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      this.logger.error('❌ Workflow execution failed', {
        workflowId: context.workflowId,
        executionId: context.executionId,
        duration,
        error: error.message,
      });

      return {
        success: false,
        executionId: context.executionId,
        startTime,
        endTime,
        duration,
        error: error.message,
      };
    } finally {
      this.activeExecutions.delete(context.executionId);
    }
  }

  private async performWorkflowExecution(context: WorkflowExecutionContext): Promise<any> {
    // Workflow execution implementation
    // This would integrate with your existing workflow execution logic
    return { message: 'Workflow executed successfully' };
  }

  getActiveExecutions(): WorkflowExecutionContext[] {
    return Array.from(this.activeExecutions.values());
  }

  async cancelExecution(executionId: string): Promise<void> {
    const context = this.activeExecutions.get(executionId);
    if (!context) {
      throw new Error(`Execution ${executionId} not found`);
    }

    this.logger.info('🛑 Cancelling workflow execution', { executionId });

    // Cancel execution logic here...
    this.activeExecutions.delete(executionId);

    this.logger.info('✅ Workflow execution cancelled', { executionId });
  }
}
```

### Phase 5: Package Exports and Integration (Priority: High)

#### Task 5.1: Update Package Exports

```typescript
// src/index.ts (Enhanced)
import 'reflect-metadata';

// Services (DI-enabled)
export * from './services/config.service';
export * from './services/credential.service';

// Browser Services (DI-enabled)
export * from './browser/browser-manager.factory';
export * from './browser/browser-manager';
export * from './browser/page-controller.service';

// Execution Engine Services (DI-enabled)
export * from './execution-engine/workflow-executor.factory';
export * from './execution-engine/workflow-executor';
export * from './execution-engine/scheduled-task-manager.service';

// Collectors (to be migrated)
export * from './collectors';

// DevTools (to be migrated)
export * from './devtools';

// Reporting (to be migrated)
export * from './reporting';

// Errors (unchanged)
export * from './errors';

// Events (unchanged)
export * from './events';

// Legacy exports for backward compatibility
export { BrowserManager } from './browser/BrowserManager';
export { PageController } from './browser/PageController';

// Types and interfaces
export type {
  BrowserInstance,
  WorkflowExecutionContext,
  WorkflowExecutionResult,
  CoreConfig,
} from './services/config.service';
```

## Usage Examples

### Basic Service Usage with DI

```typescript
// In a domain service (e.g., CLI package)
import { Service } from '@gygalogin/decorators/di';
import { BrowserManagerFactory, CredentialService } from '@gygalogin/core';

@Service()
export class ProfileBrowserService {
  private browserManager: BrowserManager;

  constructor(
    private readonly browserManagerFactory: BrowserManagerFactory,
    private readonly credentialService: CredentialService
  ) {
    // Each service gets its own isolated browser manager
    this.browserManager = browserManagerFactory.createBrowserManager('profile-service');
  }

  async launchProfileBrowser(profileId: string): Promise<string> {
    // Services are automatically injected and ready to use
    const profile = await this.getProfile(profileId);

    // Launch browser with profile using isolated manager
    const instanceId = await this.browserManager.launchBrowser(profile);

    return instanceId;
  }

  async getManagerStats() {
    return this.browserManager.getManagerStats();
  }

  async executeWorkflowWithCredentials(workflowId: string, credentialNames: string[]) {
    // Load credentials
    const credentials: Record<string, any> = {};
    for (const name of credentialNames) {
      credentials[name] = await this.credentialService.getCredentials(name);
    }

    // Execute workflow with loaded credentials
    // Implementation here...
  }
}
```

### Core Service Integration

```typescript
// In application startup (e.g., CLI or API entry point)
import { Container } from '@gygalogin/decorators/di';
import { ConfigService, BrowserManagerFactory, ScheduledTaskManagerService } from '@gygalogin/core';

async function initializeCore() {
  const configService = Container.get(ConfigService);
  const browserFactory = Container.get(BrowserManagerFactory);
  const taskManager = Container.get(ScheduledTaskManagerService);

  // Create a browser manager for the main application
  const mainBrowserManager = browserFactory.createBrowserManager('main-app');

  // Services are automatically configured and ready
  console.log('✅ Core services initialized');
  console.log(`Browser config: ${JSON.stringify(configService.browser)}`);
  console.log(`Active browsers: ${mainBrowserManager.getActiveBrowsers().length}`);
  console.log(`Manager stats: ${JSON.stringify(mainBrowserManager.getManagerStats())}`);
}
```

### Workflow Execution with DI

```typescript
// In a workflow service
import { Service } from '@gygalogin/decorators/di';
import { WorkflowExecutorFactory, BrowserManagerFactory } from '@gygalogin/core';

@Service()
export class WorkflowService {
  private activeExecutors = new Map<string, WorkflowExecutor>();

  constructor(
    private readonly workflowExecutorFactory: WorkflowExecutorFactory,
    private readonly browserManagerFactory: BrowserManagerFactory
  ) {}

  async runAutomationWorkflow(workflowId: string, profileId: string): Promise<any> {
    // Create isolated executor for this workflow execution
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const executor = this.workflowExecutorFactory.createWorkflowExecutor(executionId);

    // Track active executor
    this.activeExecutors.set(executionId, executor);

    try {
      // Create execution context
      const context = {
        workflowId,
        executionId,
        profileId,
        credentials: {
          'web-service': 'my-api-credentials',
          database: 'db-credentials',
        },
      };

      // Execute workflow with isolated executor and browser management
      const result = await executor.executeWorkflow(context);

      if (!result.success) {
        throw new Error(`Workflow execution failed: ${result.error}`);
      }

      return result.data;
    } finally {
      // Cleanup executor and its resources
      await executor.cleanup();
      this.activeExecutors.delete(executionId);
    }
  }

  async getExecutionStats(executionId: string) {
    const executor = this.activeExecutors.get(executionId);
    return executor ? executor.getExecutionStats() : null;
  }

  async cancelExecution(executionId: string) {
    const executor = this.activeExecutors.get(executionId);
    if (executor) {
      await executor.cleanup();
      this.activeExecutors.delete(executionId);
    }
  }

  async getActiveExecutions() {
    return Array.from(this.activeExecutors.keys());
  }

  async cleanupAllExecutions() {
    const cleanupPromises = Array.from(this.activeExecutors.values()).map(executor =>
      executor.cleanup()
    );

    await Promise.allSettled(cleanupPromises);
    this.activeExecutors.clear();
  }
}
```

## Migration Strategy

### Phase 1: Preparation (Week 1)

1. **Add Dependencies**: Update `package.json` with decorators and reflect-metadata
2. **Update TypeScript**: Enable decorator support in `tsconfig.json`
3. **Create Core Services**: Implement `ConfigService`, enhance `CredentialService`
4. **Test Infrastructure**: Ensure new services work with existing functionality

### Phase 2: Browser Services Migration (Week 2)

1. **Migrate BrowserManagerService**: Convert to DI pattern with `@Service()` decorator
2. **Migrate PageControllerService**: Add DI support and service integration
3. **Update Tests**: Ensure existing browser tests still pass
4. **Backward Compatibility**: Keep legacy exports working

### Phase 3: Execution Engine Migration (Week 3)

1. **Migrate ScheduledTaskManagerService**: Convert to DI with configuration injection
2. **Migrate WorkflowExecutorService**: Add DI support with service dependencies
3. **Integration Testing**: Test execution engine with DI container
4. **Performance Testing**: Ensure DI doesn't impact execution performance

### Phase 4: DevTools and Collectors Migration (Week 4)

#### Task 4.1: Analyze DevTools Pattern

```typescript
// Option 1: DevTools Factory (if per-browser attachment)
@Service()
export class DevToolsListenerFactory {
  createListener(browserId: string): DevToolsListener {
    return new DevToolsListener(browserId);
  }
}

// Option 2: DevTools Singleton (if global monitoring)
@Service()
export class DevToolsListener {
  // Global DevTools monitoring service
}
```

#### Task 4.2: Keep Data Collectors as Singletons

```typescript
// src/collectors/browser-data-collector.service.ts
@Service()
export class BrowserDataCollectorManager {
  // Singleton for centralized data collection
  // Aggregates data from multiple browser instances
}
```

### Phase 5: Integration and Cleanup (Week 5)

1. **CLI Integration**: Test core services work with CLI domain services
2. **API Integration**: Test core services work with API controllers
3. **DB Integration**: Test core services work with database repositories
4. **DevTools Decision**: Implement factory or singleton based on usage pattern
5. **Documentation**: Update usage examples and migration guides
6. **Legacy Cleanup**: Remove old patterns if no longer needed

## Benefits of Core DI Integration

### ✅ **Development Benefits:**

1. **Automatic Injection** - No manual service instantiation
2. **Configuration Management** - Centralized config with environment support
3. **Testability** - Easy mocking and testing with DI container
4. **Consistency** - Same patterns across all packages

### ✅ **Architecture Benefits:**

1. **Service Composition** - Services automatically compose with dependencies
2. **Single Responsibility** - Clear separation of concerns
3. **Scalability** - Easy to add new services and features
4. **Maintainability** - Centralized dependency management

### ✅ **Integration Benefits:**

1. **CLI Package** - Core services automatically available in domain services
2. **API Package** - Core services injectable in controllers
3. **DB Package** - Seamless integration with database services
4. **Cross-Package** - Consistent DI patterns across entire monorepo

### ✅ **Production Benefits:**

1. **Performance** - Singleton services, efficient resource management
2. **Reliability** - Proper service lifecycle and error handling
3. **Monitoring** - Built-in logging and configuration management
4. **Deployment** - Easy configuration and environment management

### ✅ **Multi-Instance Browser Benefits:**

1. **Isolation** - Each workflow/service gets its own browser manager
2. **Scalability** - No shared state bottlenecks between concurrent operations
3. **Fault Tolerance** - Browser crashes in one manager don't affect others
4. **Resource Control** - Fine-grained control over browser resource allocation
5. **Monitoring** - Per-manager statistics and health monitoring
6. **Thread Safety** - No race conditions between different execution contexts

### 🏗️ **Factory Pattern Advantages:**

```typescript
// Different browser managers for different use cases
const workflowManager = factory.createWorkflowBrowserManager('workflow-123');
const cliManager = factory.createCliBrowserManager();
const apiManager = factory.createBrowserManager('api-service');

// Each manager is completely isolated
workflowManager.launchBrowser(profile1); // Independent of others
cliManager.launchBrowser(profile2); // Independent of others
apiManager.launchBrowser(profile3); // Independent of others

// Get isolated statistics
console.log('Workflow browsers:', workflowManager.getManagerStats());
console.log('CLI browsers:', cliManager.getManagerStats());
console.log('API browsers:', apiManager.getManagerStats());
```

### 🔄 **Concurrent Execution Safety:**

- **No Singleton Bottlenecks** - Each execution context gets its own browser manager
- **Independent Resource Pools** - Browsers don't compete across different workflows
- **Isolated Failure Domains** - One workflow's browser issues don't affect others
- **Scalable Architecture** - Can handle multiple concurrent workflows safely

This DI integration transforms your Core package into a modern, injectable service layer that provides the foundation for browser automation, workflow execution, and credential management across your entire anti-detect system, with full support for concurrent multi-instance browser operations.
