interface ICredential {
    id: string;
    name: string;
    type: string;
    data: any;
    createdAt?: Date;
    updatedAt?: Date;
}
interface ICredentialDataDecryptedObject {
    [key: string]: any;
}
interface ICredentialTestRequest {
    credentialId: string;
    nodeType?: string;
}
interface ICredentialTestResult {
    success?: boolean;
    status?: string;
    message?: string;
    error?: string;
}
export interface ICredentialRepository {
    findById(id: string): Promise<ICredential | null>;
    findByName(name: string): Promise<ICredential | null>;
    findByType(type: string): Promise<ICredential[]>;
    create(credential: Omit<ICredential, 'id' | 'createdAt' | 'updatedAt'>): Promise<ICredential>;
    update(id: string, updates: Partial<ICredential>): Promise<ICredential>;
    delete(id: string): Promise<void>;
    testCredentials(request: ICredentialTestRequest): Promise<ICredentialTestResult>;
}
export interface ICredentialEncryption {
    encrypt(data: ICredentialDataDecryptedObject): Promise<string>;
    decrypt(encryptedData: string): Promise<ICredentialDataDecryptedObject>;
    hash(data: string): Promise<string>;
    verify(data: string, hash: string): Promise<boolean>;
}
export interface ICredentialServiceOptions {
    repository?: ICredentialRepository;
    encryption?: ICredentialEncryption;
    cacheEnabled?: boolean;
    cacheTTL?: number;
}
export declare class CredentialService {
    private repository?;
    private encryption?;
    private cache;
    private cacheEnabled;
    private cacheTTL;
    constructor(options?: ICredentialServiceOptions);
    /**
     * Get credentials by name for the current execution context
     */
    getCredentials(name: string): Promise<ICredentialDataDecryptedObject>;
    /**
     * Get credentials by ID
     */
    getCredentialsById(id: string): Promise<ICredentialDataDecryptedObject>;
    /**
     * Store new credentials
     */
    storeCredentials(name: string, type: string, data: ICredentialDataDecryptedObject): Promise<ICredential>;
    /**
     * Update existing credentials
     */
    updateCredentials(id: string, updates: Partial<Pick<ICredential, 'name' | 'type' | 'data'>>): Promise<ICredential>;
    /**
     * Delete credentials
     */
    deleteCredentials(id: string, name?: string): Promise<void>;
    /**
     * Test credentials
     */
    testCredentials(request: ICredentialTestRequest): Promise<ICredentialTestResult>;
    /**
     * List credentials by type
     */
    getCredentialsByType(type: string): Promise<ICredential[]>;
    /**
     * Clear all cached credentials
     */
    clearCache(): void;
    /**
     * Clear expired cache entries
     */
    cleanExpiredCache(): void;
    /**
     * Set credential repository
     */
    setRepository(repository: ICredentialRepository): void;
    /**
     * Set encryption service
     */
    setEncryption(encryption: ICredentialEncryption): void;
    private getCachedCredential;
    private setCachedCredential;
}
export declare function getCredentialService(): CredentialService;
export declare function setDefaultCredentialService(service: CredentialService): void;
export declare class MockCredentialRepository implements ICredentialRepository {
    private credentials;
    findById(id: string): Promise<ICredential | null>;
    findByName(name: string): Promise<ICredential | null>;
    findByType(type: string): Promise<ICredential[]>;
    create(credentialData: Omit<ICredential, 'id' | 'createdAt' | 'updatedAt'>): Promise<ICredential>;
    update(id: string, updates: Partial<ICredential>): Promise<ICredential>;
    delete(id: string): Promise<void>;
    testCredentials(request: ICredentialTestRequest): Promise<ICredentialTestResult>;
}
export declare class MockCredentialEncryption implements ICredentialEncryption {
    encrypt(data: ICredentialDataDecryptedObject): Promise<string>;
    decrypt(encryptedData: string): Promise<ICredentialDataDecryptedObject>;
    hash(data: string): Promise<string>;
    verify(data: string, hash: string): Promise<boolean>;
}
export {};
//# sourceMappingURL=credential.service.d.ts.map