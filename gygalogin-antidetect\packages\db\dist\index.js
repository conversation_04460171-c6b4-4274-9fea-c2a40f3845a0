"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataSourceProvider = exports.AppDataSource = exports.ExecutionMode = exports.ExecutionStatus = exports.TransactionService = exports.MigrationService = exports.DatabaseService = exports.ScheduledTaskRepository = exports.ExecutionRepository = exports.WorkflowRepository = exports.ProxyRepository = exports.ProfileRepository = exports.BaseRepository = exports.ScheduledTaskEntity = exports.ExecutionEntity = exports.WorkflowEntity = exports.SessionEntity = exports.ProxyEntity = exports.ProfileEntity = exports.database = exports.DB = void 0;
require("reflect-metadata");
const connection_1 = require("./connection");
const profile_entity_1 = require("./entities/profile.entity");
Object.defineProperty(exports, "ProfileEntity", { enumerable: true, get: function () { return profile_entity_1.ProfileEntity; } });
const proxy_entity_1 = require("./entities/proxy.entity");
Object.defineProperty(exports, "ProxyEntity", { enumerable: true, get: function () { return proxy_entity_1.ProxyEntity; } });
const session_entity_1 = require("./entities/session.entity");
Object.defineProperty(exports, "SessionEntity", { enumerable: true, get: function () { return session_entity_1.SessionEntity; } });
const workflow_entity_1 = require("./entities/workflow.entity");
Object.defineProperty(exports, "WorkflowEntity", { enumerable: true, get: function () { return workflow_entity_1.WorkflowEntity; } });
const execution_entity_1 = require("./entities/execution.entity");
Object.defineProperty(exports, "ExecutionEntity", { enumerable: true, get: function () { return execution_entity_1.ExecutionEntity; } });
const scheduled_task_entity_1 = require("./entities/scheduled-task.entity");
Object.defineProperty(exports, "ScheduledTaskEntity", { enumerable: true, get: function () { return scheduled_task_entity_1.ScheduledTaskEntity; } });
const profile_repository_1 = require("./repositories/profile.repository");
Object.defineProperty(exports, "ProfileRepository", { enumerable: true, get: function () { return profile_repository_1.ProfileRepository; } });
const proxy_repository_1 = require("./repositories/proxy.repository");
Object.defineProperty(exports, "ProxyRepository", { enumerable: true, get: function () { return proxy_repository_1.ProxyRepository; } });
const workflow_repository_1 = require("./repositories/workflow.repository");
Object.defineProperty(exports, "WorkflowRepository", { enumerable: true, get: function () { return workflow_repository_1.WorkflowRepository; } });
const execution_repository_1 = require("./repositories/execution.repository");
Object.defineProperty(exports, "ExecutionRepository", { enumerable: true, get: function () { return execution_repository_1.ExecutionRepository; } });
const scheduled_task_repository_1 = require("./repositories/scheduled-task.repository");
Object.defineProperty(exports, "ScheduledTaskRepository", { enumerable: true, get: function () { return scheduled_task_repository_1.ScheduledTaskRepository; } });
const base_repository_1 = require("./repositories/base.repository");
Object.defineProperty(exports, "BaseRepository", { enumerable: true, get: function () { return base_repository_1.BaseRepository; } });
// Import DI services
const database_service_1 = require("./services/database.service");
Object.defineProperty(exports, "DatabaseService", { enumerable: true, get: function () { return database_service_1.DatabaseService; } });
const migration_service_1 = require("./services/migration.service");
Object.defineProperty(exports, "MigrationService", { enumerable: true, get: function () { return migration_service_1.MigrationService; } });
const transaction_service_1 = require("./services/transaction.service");
Object.defineProperty(exports, "TransactionService", { enumerable: true, get: function () { return transaction_service_1.TransactionService; } });
// Create concrete repository classes for entities without custom repositories
class SessionRepository extends base_repository_1.BaseRepository {
    constructor(repository) {
        super(repository);
    }
}
class DB {
    constructor(dataSource) {
        this._dataSource = dataSource;
        // Use the new DI-enabled repositories with DataSource injection
        this.profileRepository = new profile_repository_1.ProfileRepository(dataSource);
        this.proxyRepository = new proxy_repository_1.ProxyRepository(dataSource);
        this.sessionRepository = new SessionRepository(dataSource.getRepository(session_entity_1.SessionEntity));
        this.workflowRepository = new workflow_repository_1.WorkflowRepository(dataSource);
        this.executionRepository = new execution_repository_1.ExecutionRepository(dataSource);
        this.taskRepository = new scheduled_task_repository_1.ScheduledTaskRepository(dataSource);
    }
    async initialize() {
        try {
            if (!this._dataSource.isInitialized) {
                await this._dataSource.initialize();
                console.log('Data Source initialized');
            }
        }
        catch (err) {
            console.error('Error during Data Source initialization:', err);
            throw err;
        }
    }
    async close() {
        if (this._dataSource.isInitialized) {
            await this._dataSource.destroy();
            console.log('Data Source closed');
        }
    }
    get profiles() {
        return this.profileRepository;
    }
    get proxies() {
        return this.proxyRepository;
    }
    get sessions() {
        return this.sessionRepository;
    }
    get workflows() {
        return this.workflowRepository;
    }
    get executions() {
        return this.executionRepository;
    }
    get tasks() {
        return this.taskRepository;
    }
    get dataSource() {
        return this._dataSource;
    }
}
exports.DB = DB;
// Create and export the main database instance
exports.database = new DB(connection_1.AppDataSource);
// Export enums
var execution_entity_2 = require("./entities/execution.entity");
Object.defineProperty(exports, "ExecutionStatus", { enumerable: true, get: function () { return execution_entity_2.ExecutionStatus; } });
Object.defineProperty(exports, "ExecutionMode", { enumerable: true, get: function () { return execution_entity_2.ExecutionMode; } });
// Export connection (enhanced with DI support)
var connection_2 = require("./connection");
Object.defineProperty(exports, "AppDataSource", { enumerable: true, get: function () { return connection_2.AppDataSource; } });
Object.defineProperty(exports, "DataSourceProvider", { enumerable: true, get: function () { return connection_2.DataSourceProvider; } });
//# sourceMappingURL=index.js.map