{"version": 3, "file": "enhanced-node-executor.js", "sourceRoot": "", "sources": ["../../src/execution-engine/enhanced-node-executor.ts"], "names": [], "mappings": ";AAAA,uDAAuD;;;;;;;;;;;;AAEvD,+CAA2C;AAG3C,0FAAqF;AACrF,0EAAmE;AACnE,qEAAqF;AACrF,iDAA6C;AAC7C,sCAMmB;AACnB,qDAK0B;AAoB1B;;GAEG;AACH,MAAa,oBAAoB;IAI/B,YACU,QAAkB,EAClB,YAAkC,EAClC,UAAwC,EAAE,EAC1C,WAAyB;QAHzB,aAAQ,GAAR,QAAQ,CAAU;QAClB,iBAAY,GAAZ,YAAY,CAAsB;QAClC,YAAO,GAAP,OAAO,CAAmC;QAC1C,gBAAW,GAAX,WAAW,CAAc;QAEjC,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACU,WAAW;6DACtB,IAAW,EACX,SAA+B,EAC/B,OAAuB,EACvB,cAA4C,EAAE;;YAE9C,MAAM,aAAa,mCAAQ,IAAI,CAAC,OAAO,GAAK,WAAW,CAAE,CAAC;YAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,iCAAiC;YACjC,IAAI,MAAA,IAAI,CAAC,WAAW,0CAAE,OAAO,EAAE,CAAC;gBAC9B,MAAM,IAAI,0BAAW,CAAC,8BAA8B,CAAC,CAAC;YACxD,CAAC;YAED,6BAA6B;YAC7B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,4CAAmB,CAAC,OAAO,CAAC,CAAC;YAEtE,wDAAwD;YACxD,MAAM,iBAAiB,GAAI,IAAY,CAAC,WAAyC,CAAC;YAClF,MAAM,aAAa,GAAG,aAAa,CAAC,aAAa,IAAI,iBAAiB,CAAC;YACvE,MAAM,UAAU,GAAG,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,KAAK,KAAI,aAAa,CAAC,OAAO,IAAI,CAAC,CAAC;YAEtE,IAAI,SAAS,GAAiB,IAAI,CAAC;YACnC,MAAM,cAAc,GAAY,EAAE,CAAC;YAEnC,2CAA2C;YAC3C,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;gBACvD,IAAI,CAAC;oBACH,kCAAkC;oBAClC,IAAI,MAAA,IAAI,CAAC,WAAW,0CAAE,OAAO,EAAE,CAAC;wBAC9B,MAAM,IAAI,0BAAW,CAAC,8BAA8B,CAAC,CAAC;oBACxD,CAAC;oBAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAC5C,IAAI,EACJ,SAAS,EACT,OAAO,EACP,aAAa,EACb,OAAO,CACR,CAAC;oBAEF,qCAAqC;oBACrC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;oBAEjD,uDAAuD;oBACvD,IAAI,IAAI,CAAC,YAAY,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;wBACrC,iCAAiC;wBACjC,IAAI,CAAC,YAAY,CAAC,WAAW,CAC3B,IAAI,KAAK,CAAC,gCAAgC,CAAC,EAC3C,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,OAAO,CAAC,aAAa,EAAE,IAAI,SAAS,EACpC,OAAO,CAAC,cAAc,EAAE,IAAI,SAAS,EACrC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,EAC1C,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE,CAC7D,CAAC;oBACJ,CAAC;oBAED,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,SAAS,GAAG,KAAc,CAAC;oBAC3B,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAE/B,sCAAsC;oBACtC,IAAI,KAAK,YAAY,0BAAW,EAAE,CAAC;wBACjC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;wBAC/C,MAAM,KAAK,CAAC;oBACd,CAAC;oBAED,8BAA8B;oBAC9B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;wBACtB,IAAI,CAAC,YAAY,CAAC,WAAW,CAC3B,SAAS,EACT,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,OAAO,CAAC,aAAa,EAAE,IAAI,SAAS,EACpC,OAAO,CAAC,cAAc,EAAE,IAAI,SAAS,EACrC,SAAS,EACT,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE,CACjD,CAAC;oBACJ,CAAC;oBAED,kEAAkE;oBAClE,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC;wBAC3B,IAAI,aAAa,CAAC,mBAAmB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;4BACnE,IAAI,CAAC;gCACH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,eAAe,CACpE,SAAS,EACT,IAAI,EACJ;oCACE,UAAU,EAAE,OAAO,CAAC,aAAa,EAAE;oCACnC,WAAW,EAAE,OAAO,CAAC,cAAc,EAAE;oCACrC,OAAO;oCACP,WAAW,EAAE,UAAU;oCACvB,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC;oCAC9B,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oCACnC,SAAS;oCACT,cAAc;iCACf,CACF,CAAC;gCAEF,IAAI,cAAc,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;oCACtC,0CAA0C;oCAC1C,MAAM,kBAAkB,GAAG,cAAc,CAAC,iBAAiB,IAAI,SAAS,CAAC;oCACzE,MAAM,gBAAgB,GAAG,cAAc,CAAC,kBAAkB;wCACxD,CAAC,iCAAM,aAAa,GAAK,cAAc,CAAC,kBAAkB,EAC1D,CAAC,CAAC,aAAa,CAAC;oCAElB,IAAI,cAAc,CAAC,KAAK,EAAE,CAAC;wCACzB,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;oCACzC,CAAC;oCAED,gDAAgD;oCAChD,IAAI,CAAC;wCACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAC5C,IAAI,EACJ,kBAAkB,EAClB,OAAO,EACP,gBAAgB,EAChB,OAAO,GAAG,CAAC,CACZ,CAAC;wCAEF,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;wCACjD,OAAO,MAAM,CAAC;oCAChB,CAAC;oCAAC,OAAO,aAAa,EAAE,CAAC;wCACvB,6CAA6C;wCAC7C,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,aAAsB,EAAE,aAAa,CAAC,CAAC;oCAC3E,CAAC;gCACH,CAAC;qCAAM,IAAI,cAAc,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;oCAC5C,MAAM,UAAU,GAAG,cAAc,CAAC,cAAc,IAAI,CAAC;4CACnD,IAAI,EAAE;gDACJ,OAAO,EAAE,IAAI;gDACb,MAAM,EAAE,cAAc,CAAC,MAAM;gDAC7B,aAAa,EAAE,SAAS,CAAC,OAAO;6CACjC;4CACD,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;yCACxB,CAAC,CAAC;oCAEH,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;oCACrD,OAAO,UAAU,CAAC;gCACpB,CAAC;qCAAM,IAAI,cAAc,CAAC,MAAM,KAAK,UAAU,IAAI,cAAc,CAAC,cAAc,EAAE,CAAC;oCACjF,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,cAAc,CAAC,cAAc,CAAC,CAAC;oCACxE,OAAO,cAAc,CAAC,cAAc,CAAC;gCACvC,CAAC;4BACH,CAAC;4BAAC,OAAO,aAAa,EAAE,CAAC;gCACvB,4DAA4D;gCAC5D,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,aAAa,CAAC,CAAC;4BACxD,CAAC;wBACH,CAAC;wBAED,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;oBAC9D,CAAC;oBAED,8CAA8C;oBAC9C,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;wBACzB,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;wBAC/D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC1B,CAAC;gBACH,CAAC;YACH,CAAC;YAED,2DAA2D;YAC3D,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAC9D,CAAC;YAED,MAAM,IAAI,2BAAkB,CAC1B,gDAAgD,EAChD,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,CACV,CAAC;QACJ,CAAC;KAAA;IAED;;OAEG;IACI,gBAAgB;;QACrB,OAAO,MAAA,IAAI,CAAC,oBAAoB,0CAAE,QAAQ,EAAE,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,kBAAkB;;QACvB,OAAO,MAAA,IAAI,CAAC,YAAY,0CAAE,QAAQ,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,eAAe;;QACpB,OAAO,MAAA,IAAI,CAAC,YAAY,0CAAE,SAAS,CAAC,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,UAAmB;;QAC5C,OAAO,MAAA,IAAI,CAAC,YAAY,0CAAE,cAAc,CAAC,UAAU,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACI,OAAO;;QACZ,MAAA,IAAI,CAAC,YAAY,0CAAE,OAAO,EAAE,CAAC;IAC/B,CAAC;IAEO,uBAAuB;QAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;YACrC,IAAI,CAAC,oBAAoB,GAAG,IAAI,qCAAoB,CAClD,IAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,EAAE,CACvC,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;YACvC,MAAM,oBAAoB,GAAuB;gBAC/C,oBAAoB,EAAE,IAAI;gBAC1B,eAAe,EAAE,MAAM,EAAE,YAAY;gBACrC,iBAAiB,EAAE,OAAO,EAAE,SAAS;gBACrC,wBAAwB,EAAE,IAAI;gBAC9B,aAAa,EAAE,EAAE;gBACjB,cAAc,EAAE,EAAE;aACnB,CAAC;YAEF,IAAI,CAAC,YAAY,GAAG,IAAI,6BAAY,iCAC7B,oBAAoB,GAAK,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAC9D,CAAC;YAEF,8CAA8C;YAC9C,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC9B,IAAI,CAAC,oBAAoB,CAAC,eAAe,CACvC,KAAK,CAAC,EAAE,WAAC,OAAA,MAAA,IAAI,CAAC,YAAY,0CAAE,mBAAmB,CAAC,KAAK,CAAC,CAAA,EAAA,CACvD,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEa,oBAAoB,CAChC,IAAW,EACX,SAA+B,EAC/B,OAAuB,EACvB,OAAqC,EACrC,OAAe;;;YAEf,gCAAgC;YAChC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAElC,mCAAmC;YACnC,MAAM,QAAQ,GAAG,MAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,0CAAE,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;YAChG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,2BAAkB,CAC1B,cAAc,IAAI,CAAC,IAAI,aAAa,EACpC,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,EACjC,OAAO,CACR,CAAC;YACJ,CAAC;YAED,yCAAyC;YACzC,MAAM,WAAW,GAAG,IAAI,2CAAmB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YACnF,MAAM,gBAAgB,GAAG,IAAI,+CAAoB,CAAC,WAAW,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YAEhF,4BAA4B;YAC5B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YAC3D,IAAI,gBAAwE,CAAC;YAE7E,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACtB,MAAM,IAAI,2BAAkB,CAC1B,cAAc,IAAI,CAAC,IAAI,mCAAmC,EAC1D,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAClC,CAAC;YACJ,CAAC;YAED,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAE3D,iDAAiD;YACjD,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;gBAC3B,gBAAgB,GAAG,IAAI,CAAC,0BAA0B,CAChD,gBAAgB,EAChB,OAAO,EACP,IAAI,EACJ,OAAO,CACR,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC;YAEtC,uBAAuB;YACvB,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAEtC,qDAAqD;YACrD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtD,OAAQ,MAAiC,CAAC,IAAI,EAAE,CAAC;YACnD,CAAC;YAED,OAAO,MAA8B,CAAC;QACxC,CAAC;KAAA;IAEO,sBAAsB,CAAC,IAAW;QACxC,+CAA+C;QAC/C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,2BAAkB,CAC1B,IAAI,CAAC,EAAE,EACP,YAAY,EACZ,QAAQ,EACR,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,IAAI,CACV,CAAC;QACJ,CAAC;QAED,kDAAkD;QAClD,+CAA+C;IACjD,CAAC;IAEO,kBAAkB,CAAC,IAAS,EAAE,IAAW;QAC/C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,2BAAkB,CAC1B,wDAAwD,EACxD,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,EAAE,UAAU,EAAE,OAAO,IAAI,EAAE,CAC5B,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,eAAe,CACrB,IAAW,EACX,KAAY,EACZ,OAAqC;;QAErC,0BAA0B;QAC1B,MAAM,SAAS,GAAG,KAAK,YAAY,2BAAkB;YACnD,CAAC,CAAC,KAAK;YACP,CAAC,CAAC,IAAI,2BAAkB,CACpB,KAAK,CAAC,OAAO,EACb,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,IAAI,EACT,EAAE,aAAa,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAC9D,CAAC;QAEN,sBAAsB;QACtB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QAEnD,sCAAsC;QACtC,MAAM,cAAc,GAAG,MAAA,MAAA,OAAO,CAAC,cAAc,mCAAI,IAAI,CAAC,cAAc,mCAAI,KAAK,CAAC;QAE9E,IAAI,cAAc,EAAE,CAAC;YACnB,6BAA6B;YAC7B,MAAM,SAAS,GAAyB,CAAC;oBACvC,IAAI,EAAE;wBACJ,KAAK,EAAE,SAAS,CAAC,OAAO;wBACxB,SAAS,EAAE,SAAS,CAAC,IAAI;wBACzB,SAAS,EAAE,SAAS,CAAC,WAAW,CAAC,IAAI;wBACrC,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;wBACnB,QAAQ,EAAE,IAAI,CAAC,IAAI;wBACnB,KAAK,EAAE,SAAS,CAAC,KAAK;wBACtB,OAAO,EAAE,SAAS,CAAC,OAAO;wBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;oBACD,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;iBACxB,CAAC,CAAC;YAEH,sCAAsC;YACtC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;YAEpD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,wDAAwD;QACxD,MAAM,SAAS,CAAC;IAClB,CAAC;IAEO,0BAA0B,CAChC,OAAmB,EACnB,SAAiB,EACjB,IAAW,EACX,OAAe;QAEf,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,MAAM,CAAC,IAAI,yBAAgB,CACzB,IAAI,CAAC,EAAE,EACP,SAAS,EACT,IAAI,CAAC,IAAI,EACT,EAAE,OAAO,EAAE,CACZ,CAAC,CAAC;YACL,CAAC,EAAE,SAAS,CAAC,CAAC;YAEd,OAAO;iBACJ,IAAI,CAAC,MAAM,CAAC,EAAE;gBACb,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE;gBACb,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB,CAAC,OAAe,EAAE,aAA8B;QACzE,MAAM,SAAS,GAAG,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,KAAK,KAAI,IAAI,CAAC,CAAC,mBAAmB;QACnE,MAAM,WAAW,GAAG,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,KAAI,aAAa,CAAC;QAE5D,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,QAAQ;gBACX,OAAO,SAAS,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YACnC,KAAK,aAAa,CAAC;YACnB;gBACE,4CAA4C;gBAC5C,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,2EAA2E;IACpE,MAAM,CAAC,eAAe,CAC3B,SAA+B,EAC/B,UAAgC;QAEhC,OAAO,0BAAW,CAAC,eAAe,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IAC5D,CAAC;CACF;AA9bD,oDA8bC"}