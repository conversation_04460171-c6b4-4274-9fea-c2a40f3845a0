import { ProfileEntity } from './profile.entity';
export declare class ProxyEntity {
    id: string;
    host: string;
    port: number;
    type: 'http' | 'https' | 'socks4' | 'socks5';
    username?: string;
    password?: string;
    country?: string;
    city?: string;
    isp?: string;
    enableRotation: boolean;
    rotationInterval: number;
    backupProxies?: Array<{
        type: 'http' | 'https' | 'socks4' | 'socks5';
        host: string;
        port: number;
        username?: string;
        password?: string;
        priority: number;
    }>;
    healthScore: number;
    lastChecked?: Date;
    blacklistedUntil?: Date;
    enabled: boolean;
    get isActive(): boolean;
    set isActive(value: boolean);
    description?: string;
    lastUsed?: Date;
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
    profiles: ProfileEntity[];
}
//# sourceMappingURL=proxy.entity.d.ts.map