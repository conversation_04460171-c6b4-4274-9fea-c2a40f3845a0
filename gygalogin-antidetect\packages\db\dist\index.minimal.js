"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecutionMode = exports.ExecutionStatus = exports.AppDataSource = exports.MinimalProfileRepository = exports.BaseRepository = exports.ScheduledTaskEntity = exports.ExecutionEntity = exports.WorkflowEntity = exports.SessionEntity = exports.ProxyEntity = exports.ProfileEntity = exports.database = exports.DB = void 0;
require("reflect-metadata");
const typeorm_1 = require("typeorm");
const connection_1 = require("./connection");
Object.defineProperty(exports, "AppDataSource", { enumerable: true, get: function () { return connection_1.AppDataSource; } });
const profile_entity_1 = require("./entities/profile.entity");
Object.defineProperty(exports, "ProfileEntity", { enumerable: true, get: function () { return profile_entity_1.ProfileEntity; } });
const proxy_entity_1 = require("./entities/proxy.entity");
Object.defineProperty(exports, "ProxyEntity", { enumerable: true, get: function () { return proxy_entity_1.ProxyEntity; } });
const session_entity_1 = require("./entities/session.entity");
Object.defineProperty(exports, "SessionEntity", { enumerable: true, get: function () { return session_entity_1.SessionEntity; } });
const workflow_entity_1 = require("./entities/workflow.entity");
Object.defineProperty(exports, "WorkflowEntity", { enumerable: true, get: function () { return workflow_entity_1.WorkflowEntity; } });
const execution_entity_1 = require("./entities/execution.entity");
Object.defineProperty(exports, "ExecutionEntity", { enumerable: true, get: function () { return execution_entity_1.ExecutionEntity; } });
const scheduled_task_entity_1 = require("./entities/scheduled-task.entity");
Object.defineProperty(exports, "ScheduledTaskEntity", { enumerable: true, get: function () { return scheduled_task_entity_1.ScheduledTaskEntity; } });
const base_repository_1 = require("./repositories/base.repository");
Object.defineProperty(exports, "BaseRepository", { enumerable: true, get: function () { return base_repository_1.BaseRepository; } });
// Create concrete repository classes for entities without custom repositories
class SessionRepository extends base_repository_1.BaseRepository {
    constructor(repository) {
        super(repository);
    }
}
// Basic ProfileRepository without DI decorators
class MinimalProfileRepository extends typeorm_1.Repository {
    constructor(dataSource) {
        super(profile_entity_1.ProfileEntity, dataSource.manager);
    }
    async findByName(name) {
        return this.find({
            where: {
                name: name, // Simple exact match for testing
            },
        });
    }
    async findActive() {
        return this.findBy({ isActive: true });
    }
}
exports.MinimalProfileRepository = MinimalProfileRepository;
class DB {
    constructor(dataSource) {
        this._dataSource = dataSource;
        this.profileRepository = new MinimalProfileRepository(dataSource);
        this.sessionRepository = new SessionRepository(dataSource.getRepository(session_entity_1.SessionEntity));
    }
    async initialize() {
        try {
            if (!this._dataSource.isInitialized) {
                await this._dataSource.initialize();
                console.log('✅ Data Source initialized');
            }
        }
        catch (err) {
            console.error('❌ Error during Data Source initialization:', err);
            throw err;
        }
    }
    async close() {
        if (this._dataSource.isInitialized) {
            await this._dataSource.destroy();
            console.log('🔌 Data Source closed');
        }
    }
    get profiles() {
        return this.profileRepository;
    }
    get sessions() {
        return this.sessionRepository;
    }
    get dataSource() {
        return this._dataSource;
    }
}
exports.DB = DB;
// Create and export the main database instance
exports.database = new DB(connection_1.AppDataSource);
// Export enums
var execution_entity_2 = require("./entities/execution.entity");
Object.defineProperty(exports, "ExecutionStatus", { enumerable: true, get: function () { return execution_entity_2.ExecutionStatus; } });
Object.defineProperty(exports, "ExecutionMode", { enumerable: true, get: function () { return execution_entity_2.ExecutionMode; } });
//# sourceMappingURL=index.minimal.js.map