"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecutionRepository = void 0;
const decorators_1 = require("@gygalogin/decorators");
const typeorm_1 = require("typeorm");
const execution_entity_1 = require("../entities/execution.entity");
let ExecutionRepository = class ExecutionRepository extends typeorm_1.Repository {
    constructor(dataSource) {
        super(execution_entity_1.ExecutionEntity, dataSource.manager);
    }
    /**
     * Find entity by ID - added for compatibility with core package
     */
    async findById(id) {
        return this.findOne({ where: { id } });
    }
    /**
     * Create a new execution entity and return it
     */
    async createExecution(executionData) {
        const execution = this.create(executionData);
        return this.save(execution);
    }
    /**
     * Update execution and return the updated entity
     */
    async updateExecution(id, updateData) {
        await this.update(id, updateData);
        return this.findOneBy({ id });
    }
    /**
     * Delete execution by id and return success status
     */
    async deleteExecution(id) {
        const result = await this.delete(id);
        return (result.affected ?? 0) > 0;
    }
    /**
     * Find executions by workflow ID
     */
    async findByWorkflowId(workflowId, limit) {
        const query = this.createQueryBuilder('execution')
            .where('execution.workflowId = :workflowId', { workflowId })
            .orderBy('execution.startedAt', 'DESC');
        if (limit) {
            query.take(limit);
        }
        return query.getMany();
    }
    /**
     * Find executions by status
     */
    async findByStatus(status) {
        return this.findBy({ status });
    }
    /**
     * Find running executions
     */
    async findRunning() {
        return this.findByStatus(execution_entity_1.ExecutionStatus.RUNNING);
    }
    /**
     * Find executions that have been running for too long (possibly stuck)
     */
    async findStuckExecutions(timeoutMinutes = 60) {
        const timeoutThreshold = new Date();
        timeoutThreshold.setMinutes(timeoutThreshold.getMinutes() - timeoutMinutes);
        return this.createQueryBuilder('execution')
            .where('execution.status = :status', { status: execution_entity_1.ExecutionStatus.RUNNING })
            .andWhere('execution.startedAt < :threshold', { threshold: timeoutThreshold })
            .getMany();
    }
    /**
     * Search executions with multiple criteria
     */
    async search(criteria, options) {
        const queryBuilder = this.createQueryBuilder('execution');
        if (criteria.workflowId) {
            queryBuilder.andWhere('execution.workflowId = :workflowId', {
                workflowId: criteria.workflowId,
            });
        }
        if (criteria.status) {
            queryBuilder.andWhere('execution.status = :status', { status: criteria.status });
        }
        if (criteria.mode) {
            queryBuilder.andWhere('execution.mode = :mode', { mode: criteria.mode });
        }
        if (criteria.startedAfter) {
            queryBuilder.andWhere('execution.startedAt >= :startedAfter', {
                startedAfter: criteria.startedAfter,
            });
        }
        if (criteria.startedBefore) {
            queryBuilder.andWhere('execution.startedAt <= :startedBefore', {
                startedBefore: criteria.startedBefore,
            });
        }
        if (criteria.finishedAfter) {
            queryBuilder.andWhere('execution.finishedAt >= :finishedAfter', {
                finishedAfter: criteria.finishedAfter,
            });
        }
        if (criteria.finishedBefore) {
            queryBuilder.andWhere('execution.finishedAt <= :finishedBefore', {
                finishedBefore: criteria.finishedBefore,
            });
        }
        if (options?.take) {
            queryBuilder.take(options.take);
        }
        if (options?.skip) {
            queryBuilder.skip(options.skip);
        }
        // Default order by startedAt DESC
        queryBuilder.orderBy('execution.startedAt', 'DESC');
        if (options?.order) {
            queryBuilder.orderBy({});
            Object.entries(options.order).forEach(([key, direction]) => {
                queryBuilder.addOrderBy(`execution.${key}`, direction);
            });
        }
        return queryBuilder.getMany();
    }
    /**
     * Start a new execution
     */
    async startExecution(workflowId, mode = execution_entity_1.ExecutionMode.MANUAL) {
        const execution = this.create({
            workflowId,
            status: execution_entity_1.ExecutionStatus.RUNNING,
            mode,
            startedAt: new Date(),
            data: {
                resultData: {},
                executionData: {},
            },
        });
        return this.save(execution);
    }
    /**
     * Mark execution as successful
     */
    async markAsSuccess(id, resultData) {
        const updateData = {
            status: execution_entity_1.ExecutionStatus.SUCCESS,
            finishedAt: new Date(),
        };
        if (resultData) {
            updateData.data = resultData;
        }
        await this.update(id, updateData);
        return this.findOneBy({ id });
    }
    /**
     * Mark execution as failed
     */
    async markAsError(id, error, errorData) {
        const updateData = {
            status: execution_entity_1.ExecutionStatus.ERROR,
            finishedAt: new Date(),
            error,
        };
        if (errorData) {
            updateData.data = errorData;
        }
        await this.update(id, updateData);
        return this.findOneBy({ id });
    }
    /**
     * Mark execution as cancelled
     */
    async markAsCancelled(id) {
        await this.update(id, {
            status: execution_entity_1.ExecutionStatus.CANCELED,
            finishedAt: new Date(),
        });
        return this.findOneBy({ id });
    }
    /**
     * Update execution data during runtime
     */
    async updateExecutionData(id, data) {
        await this.update(id, { data });
    }
    /**
     * Get execution statistics
     */
    async getStats(timeframeHours = 24) {
        const total = await this.count();
        const running = await this.countBy({ status: execution_entity_1.ExecutionStatus.RUNNING });
        const success = await this.countBy({ status: execution_entity_1.ExecutionStatus.SUCCESS });
        const error = await this.countBy({ status: execution_entity_1.ExecutionStatus.ERROR });
        const cancelled = await this.countBy({ status: execution_entity_1.ExecutionStatus.CANCELED });
        const timeframeStart = new Date();
        timeframeStart.setHours(timeframeStart.getHours() - timeframeHours);
        const executionsInTimeframe = await this.createQueryBuilder('execution')
            .where('execution.startedAt >= :timeframeStart', { timeframeStart })
            .getCount();
        // Calculate average execution time for completed executions
        const completedExecutions = await this.createQueryBuilder('execution')
            .select("AVG(CAST(strftime('%s', execution.finishedAt) AS INTEGER) - CAST(strftime('%s', execution.startedAt) AS INTEGER))", 'avgTime')
            .where('execution.status IN (:...statuses)', {
            statuses: [execution_entity_1.ExecutionStatus.SUCCESS, execution_entity_1.ExecutionStatus.ERROR],
        })
            .andWhere('execution.finishedAt IS NOT NULL')
            .getRawOne();
        const averageExecutionTime = completedExecutions.avgTime
            ? parseFloat(completedExecutions.avgTime) * 1000
            : 0;
        return {
            total,
            running,
            success,
            error,
            cancelled,
            averageExecutionTime,
            executionsInTimeframe,
        };
    }
    /**
     * Clean up old executions
     */
    async cleanupOldExecutions(daysToKeep = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
        const result = await this.createQueryBuilder()
            .delete()
            .where('startedAt < :cutoffDate', { cutoffDate })
            .andWhere('status != :running', { running: execution_entity_1.ExecutionStatus.RUNNING })
            .execute();
        return result.affected || 0;
    }
    /**
     * Find execution with workflow details
     */
    async findWithWorkflow(id) {
        return this.createQueryBuilder('execution')
            .leftJoinAndSelect('execution.workflow', 'workflow')
            .where('execution.id = :id', { id })
            .getOne();
    }
};
exports.ExecutionRepository = ExecutionRepository;
exports.ExecutionRepository = ExecutionRepository = __decorate([
    (0, decorators_1.Service)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], ExecutionRepository);
//# sourceMappingURL=execution.repository.js.map