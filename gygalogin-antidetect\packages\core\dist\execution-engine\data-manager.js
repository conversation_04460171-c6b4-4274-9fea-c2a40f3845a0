"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataManager = void 0;
class DataManager {
    /**
     * Apply paired items to output data (improved version from NodeExecutor)
     */
    static applyPairedItem(inputData, outputData, options = {}) {
        if (!outputData || outputData.length === 0) {
            return outputData;
        }
        const mode = options.mode || 'default';
        switch (mode) {
            case 'oneToOne':
                return this.applyOneToOnePairing(inputData, outputData);
            case 'manyToOne':
                return this.applyManyToOnePairing(inputData, outputData, options.sourceItemIndex);
            case 'oneToMany':
                return this.applyOneToManyPairing(inputData, outputData);
            case 'default':
            default:
                return this.applyDefaultPairing(inputData, outputData);
        }
    }
    /**
     * Default pairing logic (from original implementation)
     */
    static applyDefaultPairing(inputData, outputData) {
        if (!inputData || inputData.length === 0) {
            return outputData.map(output => (Object.assign(Object.assign({}, output), { pairedItem: { item: 0 } })));
        }
        if (inputData.length === 1) {
            return outputData.map(output => (Object.assign(Object.assign({}, output), { pairedItem: { item: 0 } })));
        }
        if (inputData.length === outputData.length) {
            return outputData.map((output, index) => (Object.assign(Object.assign({}, output), { pairedItem: { item: index } })));
        }
        return outputData.map(output => (Object.assign(Object.assign({}, output), { pairedItem: { item: 0 } })));
    }
    /**
     * One-to-one pairing: each output item is paired with corresponding input item
     */
    static applyOneToOnePairing(inputData, outputData) {
        return outputData.map((output, index) => (Object.assign(Object.assign({}, output), { pairedItem: { item: Math.min(index, inputData.length - 1) } })));
    }
    /**
     * Many-to-one pairing: all output items are paired with one specific input item
     */
    static applyManyToOnePairing(inputData, outputData, sourceItemIndex) {
        const itemIndex = sourceItemIndex !== null && sourceItemIndex !== void 0 ? sourceItemIndex : 0;
        return outputData.map(output => (Object.assign(Object.assign({}, output), { pairedItem: { item: Math.min(itemIndex, inputData.length - 1) } })));
    }
    /**
     * One-to-many pairing: output items are distributed across input items
     */
    static applyOneToManyPairing(inputData, outputData) {
        if (!inputData || inputData.length === 0) {
            return outputData.map(output => (Object.assign(Object.assign({}, output), { pairedItem: { item: 0 } })));
        }
        return outputData.map((output, index) => (Object.assign(Object.assign({}, output), { pairedItem: { item: index % inputData.length } })));
    }
    /**
     * Merge multiple data arrays based on a key or strategy
     */
    static mergeData(dataArrays, options = {}) {
        if (!dataArrays || dataArrays.length === 0) {
            return [];
        }
        if (dataArrays.length === 1) {
            return dataArrays[0];
        }
        const { mergeBy, strategy = 'append', preserveKeys = [] } = options;
        switch (strategy) {
            case 'merge':
                return this.mergeByKey(dataArrays, mergeBy, preserveKeys);
            case 'replace':
                return this.replaceStrategy(dataArrays);
            case 'append':
            default:
                return this.appendStrategy(dataArrays);
        }
    }
    /**
     * Append all data arrays together
     */
    static appendStrategy(dataArrays) {
        const result = [];
        let itemIndex = 0;
        for (const dataArray of dataArrays) {
            for (const item of dataArray) {
                result.push(Object.assign(Object.assign({}, item), { pairedItem: { item: itemIndex++ } }));
            }
        }
        return result;
    }
    /**
     * Use the last non-empty array (replace strategy)
     */
    static replaceStrategy(dataArrays) {
        for (let i = dataArrays.length - 1; i >= 0; i--) {
            if (dataArrays[i].length > 0) {
                return dataArrays[i];
            }
        }
        return [];
    }
    /**
     * Merge arrays by a specific key
     */
    static mergeByKey(dataArrays, mergeBy, preserveKeys = []) {
        if (!mergeBy) {
            return this.appendStrategy(dataArrays);
        }
        const merged = new Map();
        for (const dataArray of dataArrays) {
            for (const item of dataArray) {
                const keyValue = item.json[mergeBy];
                if (merged.has(keyValue)) {
                    const existing = merged.get(keyValue);
                    // Merge the JSON objects
                    const mergedJson = Object.assign({}, existing.json);
                    // Add new properties, but preserve specified keys from existing
                    for (const [key, value] of Object.entries(item.json)) {
                        if (!preserveKeys.includes(key)) {
                            mergedJson[key] = value;
                        }
                    }
                    merged.set(keyValue, Object.assign(Object.assign({}, item), { json: mergedJson, pairedItem: existing.pairedItem }));
                }
                else {
                    merged.set(keyValue, item);
                }
            }
        }
        return Array.from(merged.values());
    }
    /**
     * Split data array based on criteria
     */
    static splitData(data, options = {}) {
        const { splitBy, maxItems, preserveStructure = false } = options;
        if (!splitBy && !maxItems) {
            return [data];
        }
        if (maxItems && !splitBy) {
            return this.splitByCount(data, maxItems, preserveStructure);
        }
        if (splitBy) {
            return this.splitByKey(data, splitBy, preserveStructure);
        }
        return [data];
    }
    /**
     * Split by maximum number of items per group
     */
    static splitByCount(data, maxItems, preserveStructure) {
        const result = [];
        for (let i = 0; i < data.length; i += maxItems) {
            const chunk = data.slice(i, i + maxItems);
            if (preserveStructure) {
                // Maintain pairing within chunks
                const processedChunk = chunk.map((item, index) => (Object.assign(Object.assign({}, item), { pairedItem: { item: index } })));
                result.push(processedChunk);
            }
            else {
                result.push(chunk);
            }
        }
        return result;
    }
    /**
     * Split by a key value
     */
    static splitByKey(data, splitBy, preserveStructure) {
        const groups = new Map();
        for (const item of data) {
            const keyValue = item.json[splitBy];
            if (!groups.has(keyValue)) {
                groups.set(keyValue, []);
            }
            groups.get(keyValue).push(item);
        }
        const result = [];
        for (const group of groups.values()) {
            if (preserveStructure) {
                const processedGroup = group.map((item, index) => (Object.assign(Object.assign({}, item), { pairedItem: { item: index } })));
                result.push(processedGroup);
            }
            else {
                result.push(group);
            }
        }
        return result;
    }
    /**
     * Transform data with filtering, transforming, and sorting
     */
    static transformData(data, options = {}) {
        let result = [...data];
        // Apply filter
        if (options.filter) {
            result = result.filter((item, index) => options.filter(item.json, index));
        }
        // Apply transform
        if (options.transform) {
            result = result.map((item, index) => (Object.assign(Object.assign({}, item), { json: options.transform(item.json, index) })));
        }
        // Apply sort
        if (options.sort) {
            result.sort((a, b) => options.sort(a.json, b.json));
        }
        // Re-apply pairing after transformations
        return result.map((item, index) => (Object.assign(Object.assign({}, item), { pairedItem: { item: index } })));
    }
    /**
     * Get data from specific input by name (for multi-input nodes)
     */
    static getInputDataFrom(allNodeOutputs, nodeName) {
        return allNodeOutputs[nodeName] || [];
    }
    /**
     * Get specific item by index from data array
     */
    static getInputDataByIndex(data, itemIndex) {
        return data[itemIndex];
    }
    /**
     * Validate data structure
     */
    static validateData(data) {
        const errors = [];
        if (!Array.isArray(data)) {
            errors.push('Data must be an array');
            return { isValid: false, errors };
        }
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (!item || typeof item !== 'object') {
                errors.push(`Item at index ${i} must be an object`);
                continue;
            }
            if (!item.json || typeof item.json !== 'object') {
                errors.push(`Item at index ${i} must have a 'json' property that is an object`);
            }
            if (item.pairedItem && (typeof item.pairedItem !== 'object' || typeof item.pairedItem.item !== 'number')) {
                errors.push(`Item at index ${i} has invalid pairedItem structure`);
            }
        }
        return { isValid: errors.length === 0, errors };
    }
    /**
     * Create empty data item
     */
    static createEmptyItem() {
        return {
            json: {},
            pairedItem: { item: 0 },
        };
    }
    /**
     * Clone data array deeply
     */
    static cloneData(data) {
        return data.map(item => ({
            json: JSON.parse(JSON.stringify(item.json)),
            binary: item.binary ? JSON.parse(JSON.stringify(item.binary)) : undefined,
            pairedItem: item.pairedItem ? Object.assign({}, item.pairedItem) : undefined,
        }));
    }
}
exports.DataManager = DataManager;
//# sourceMappingURL=data-manager.js.map