"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrationService = void 0;
const decorators_1 = require("@gygalogin/decorators");
const database_service_1 = require("./database.service");
const shared_1 = require("@gygalogin/shared");
let MigrationService = class MigrationService {
    constructor(databaseService, logger) {
        this.databaseService = databaseService;
        this.logger = logger;
    }
    async runMigrations() {
        const dataSource = this.databaseService.getDataSource();
        try {
            const migrations = await dataSource.runMigrations();
            this.logger.info(`✅ Ran ${migrations.length} migrations`);
        }
        catch (error) {
            this.logger.error('❌ Migration failed:', error);
            throw error;
        }
    }
    async revertLastMigration() {
        const dataSource = this.databaseService.getDataSource();
        try {
            await dataSource.undoLastMigration();
            this.logger.info('✅ Reverted last migration');
        }
        catch (error) {
            this.logger.error('❌ Migration revert failed:', error);
            throw error;
        }
    }
    async showMigrations() {
        const dataSource = this.databaseService.getDataSource();
        const hasPendingMigrations = await dataSource.showMigrations();
        this.logger.info(`📋 Pending migrations: ${hasPendingMigrations ? 'Yes' : 'No'}`);
        // Get migration information using a different approach
        try {
            const executedMigrations = await dataSource.runMigrations({ transaction: 'none' });
            this.logger.info(`✅ Total executed migrations: ${executedMigrations.length}`);
        }
        catch (error) {
            this.logger.warn('Unable to retrieve migration details:', error);
        }
    }
};
exports.MigrationService = MigrationService;
exports.MigrationService = MigrationService = __decorate([
    (0, decorators_1.Service)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService,
        shared_1.Logger])
], MigrationService);
//# sourceMappingURL=migration.service.js.map