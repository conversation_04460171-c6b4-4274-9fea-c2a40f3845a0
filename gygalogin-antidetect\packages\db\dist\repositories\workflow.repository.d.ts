import { DataSource, FindManyOptions, Repository } from 'typeorm';
import { WorkflowEntity } from '../entities/workflow.entity';
export interface WorkflowSearchCriteria {
    name?: string;
    active?: boolean;
    createdAfter?: Date;
    createdBefore?: Date;
}
export declare class WorkflowRepository extends Repository<WorkflowEntity> {
    constructor(dataSource: DataSource);
    /**
     * Find entity by ID - added for compatibility with core package
     */
    findById(id: string): Promise<WorkflowEntity | null>;
    /**
     * Create a new workflow entity and return it
     */
    createWorkflow(workflowData: Partial<WorkflowEntity>): Promise<WorkflowEntity>;
    /**
     * Update workflow and return the updated entity
     */
    updateWorkflow(id: string, updateData: Partial<WorkflowEntity>): Promise<WorkflowEntity | null>;
    /**
     * Delete workflow by id and return success status
     */
    deleteWorkflow(id: string): Promise<boolean>;
    /**
     * Find workflows by name (case-insensitive partial match)
     */
    findByName(name: string): Promise<WorkflowEntity[]>;
    /**
     * Find active workflows
     */
    findActive(): Promise<WorkflowEntity[]>;
    /**
     * Find inactive workflows
     */
    findInactive(): Promise<WorkflowEntity[]>;
    /**
     * Search workflows with multiple criteria
     */
    search(criteria: WorkflowSearchCriteria, options?: FindManyOptions<WorkflowEntity>): Promise<WorkflowEntity[]>;
    /**
     * Activate a workflow
     */
    activate(id: string): Promise<WorkflowEntity | null>;
    /**
     * Deactivate a workflow
     */
    deactivate(id: string): Promise<WorkflowEntity | null>;
    /**
     * Find workflow with its executions
     */
    findWithExecutions(id: string): Promise<WorkflowEntity | null>;
    /**
     * Get workflow statistics
     */
    getStats(): Promise<{
        total: number;
        active: number;
        inactive: number;
        recentlyCreated: number;
        totalExecutions: number;
    }>;
    /**
     * Duplicate a workflow
     */
    duplicate(id: string, newName?: string): Promise<WorkflowEntity | null>;
}
//# sourceMappingURL=workflow.repository.d.ts.map