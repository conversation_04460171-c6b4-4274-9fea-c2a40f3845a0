import { Service } from '@gygalogin/decorators';
import { DataSource, FindManyOptions, Repository } from 'typeorm';
import { WorkflowEntity } from '../entities/workflow.entity';

export interface WorkflowSearchCriteria {
  name?: string;
  active?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
}

@Service()
export class WorkflowRepository extends Repository<WorkflowEntity> {
  constructor(dataSource: DataSource) {
    super(WorkflowEntity, dataSource.manager);
  }

  /**
   * Find entity by ID - added for compatibility with core package
   */
  async findById(id: string): Promise<WorkflowEntity | null> {
    return this.findOne({ where: { id } as any });
  }

  /**
   * Create a new workflow entity and return it
   */
  async createWorkflow(workflowData: Partial<WorkflowEntity>): Promise<WorkflowEntity> {
    const workflow = this.create(workflowData);
    return this.save(workflow);
  }

  /**
   * Update workflow and return the updated entity
   */
  async updateWorkflow(
    id: string,
    updateData: Partial<WorkflowEntity>
  ): Promise<WorkflowEntity | null> {
    await this.update(id, updateData);
    return this.findOneBy({ id } as any);
  }

  /**
   * Delete workflow by id and return success status
   */
  async deleteWorkflow(id: string): Promise<boolean> {
    const result = await this.delete(id);
    return (result.affected ?? 0) > 0;
  }

  /**
   * Find workflows by name (case-insensitive partial match)
   */
  async findByName(name: string): Promise<WorkflowEntity[]> {
    return this.createQueryBuilder('workflow')
      .where('LOWER(workflow.name) LIKE LOWER(:name)', { name: `%${name}%` })
      .getMany();
  }

  /**
   * Find active workflows
   */
  async findActive(): Promise<WorkflowEntity[]> {
    return this.findBy({ active: true });
  }

  /**
   * Find inactive workflows
   */
  async findInactive(): Promise<WorkflowEntity[]> {
    return this.findBy({ active: false });
  }

  /**
   * Search workflows with multiple criteria
   */
  async search(
    criteria: WorkflowSearchCriteria,
    options?: FindManyOptions<WorkflowEntity>
  ): Promise<WorkflowEntity[]> {
    const queryBuilder = this.createQueryBuilder('workflow');

    if (criteria.name) {
      queryBuilder.andWhere('LOWER(workflow.name) LIKE LOWER(:name)', {
        name: `%${criteria.name}%`,
      });
    }

    if (criteria.active !== undefined) {
      queryBuilder.andWhere('workflow.active = :active', { active: criteria.active });
    }

    if (criteria.createdAfter) {
      queryBuilder.andWhere('workflow.createdAt >= :createdAfter', {
        createdAfter: criteria.createdAfter,
      });
    }

    if (criteria.createdBefore) {
      queryBuilder.andWhere('workflow.createdAt <= :createdBefore', {
        createdBefore: criteria.createdBefore,
      });
    }

    if (options?.take) {
      queryBuilder.take(options.take);
    }

    if (options?.skip) {
      queryBuilder.skip(options.skip);
    }

    if (options?.order) {
      Object.entries(options.order).forEach(([key, direction]) => {
        queryBuilder.addOrderBy(`workflow.${key}`, direction as 'ASC' | 'DESC');
      });
    }

    return queryBuilder.getMany();
  }

  /**
   * Activate a workflow
   */
  async activate(id: string): Promise<WorkflowEntity | null> {
    await this.update(id, { active: true });
    return this.findOneBy({ id } as any);
  }

  /**
   * Deactivate a workflow
   */
  async deactivate(id: string): Promise<WorkflowEntity | null> {
    await this.update(id, { active: false });
    return this.findOneBy({ id } as any);
  }

  /**
   * Find workflow with its executions
   */
  async findWithExecutions(id: string): Promise<WorkflowEntity | null> {
    return this.createQueryBuilder('workflow')
      .leftJoinAndSelect('workflow.executions', 'execution')
      .where('workflow.id = :id', { id })
      .orderBy('execution.startedAt', 'DESC')
      .getOne();
  }

  /**
   * Get workflow statistics
   */
  async getStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    recentlyCreated: number; // within last 7 days
    totalExecutions: number;
  }> {
    const total = await this.count();
    const active = await this.countBy({ active: true });
    const inactive = total - active;

    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentlyCreated = await this.createQueryBuilder('workflow')
      .where('workflow.createdAt >= :sevenDaysAgo', { sevenDaysAgo })
      .getCount();

    const totalExecutions = await this.createQueryBuilder('workflow')
      .leftJoin('workflow.executions', 'execution')
      .select('COUNT(execution.id)', 'count')
      .getRawOne()
      .then(result => parseInt(result.count) || 0);

    return {
      total,
      active,
      inactive,
      recentlyCreated,
      totalExecutions,
    };
  }

  /**
   * Duplicate a workflow
   */
  async duplicate(id: string, newName?: string): Promise<WorkflowEntity | null> {
    const original = await this.findOneBy({ id } as any);
    if (!original) {
      return null;
    }

    const duplicatedWorkflow = {
      name: newName || `${original.name} (Copy)`,
      nodes: original.nodes,
      connections: original.connections,
      active: false, // Duplicated workflows start as inactive
      settings: original.settings,
      staticData: original.staticData,
      tags: original.tags,
      versionId: original.versionId,
    };

    const created = this.create(duplicatedWorkflow);
    return this.save(created);
  }
}
