import { INode, INodeExecutionData, Workflow } from '@gygalogin/shared';
import { ExecuteContext } from './node-execution-context/execute-context';
import { WorkflowStateManager } from './workflow-state-manager';
import { ErrorRecoveryConfig, ErrorMonitorConfig } from './error-recovery';
interface IRetrySettings {
    count: number;
    backoff?: 'exponential' | 'linear';
    delay?: number;
}
export interface IEnhancedNodeExecutorOptions {
    timeout?: number;
    retries?: number;
    continueOnFail?: boolean;
    retrySettings?: IRetrySettings;
    enableErrorRecovery?: boolean;
    enableErrorMonitoring?: boolean;
    errorRecoveryConfig?: ErrorRecoveryConfig;
    errorMonitorConfig?: ErrorMonitorConfig;
}
/**
 * Enhanced Node Executor with advanced error handling, recovery, and monitoring
 */
export declare class EnhancedNodeExecutor {
    private workflow;
    private stateManager;
    private options;
    private abortSignal?;
    private errorRecoveryManager?;
    private errorMonitor?;
    constructor(workflow: Workflow, stateManager: WorkflowStateManager, options?: IEnhancedNodeExecutorOptions, abortSignal?: AbortSignal | undefined);
    /**
     * Execute a node with advanced error handling, recovery, and monitoring
     */
    executeNode(node: INode, inputData: INodeExecutionData[], context: ExecuteContext, nodeOptions?: IEnhancedNodeExecutorOptions): Promise<INodeExecutionData[]>;
    /**
     * Get error recovery statistics
     */
    getRecoveryStats(): import("./error-recovery").ErrorRecoveryStats | undefined;
    /**
     * Get error monitoring statistics
     */
    getMonitoringStats(): import("./error-recovery").ErrorStats | undefined;
    /**
     * Get recent error alerts
     */
    getRecentAlerts(): import("./error-recovery").ErrorAlert[] | undefined;
    /**
     * Generate error report
     */
    generateErrorReport(timeWindow?: number): {
        summary: import("./error-recovery").ErrorStats;
        topErrors: Array<{
            message: string;
            count: number;
            nodes: string[];
        }>;
        recommendations: string[];
    } | undefined;
    /**
     * Cleanup resources
     */
    destroy(): void;
    private initializeErrorHandling;
    private attemptNodeExecution;
    private validateNodeParameters;
    private validateOutputData;
    private handleNodeError;
    private executeWithEnhancedTimeout;
    private calculateRetryDelay;
    private delay;
    static applyPairedItem(inputData: INodeExecutionData[], outputData: INodeExecutionData[]): INodeExecutionData[];
}
export {};
//# sourceMappingURL=enhanced-node-executor.d.ts.map