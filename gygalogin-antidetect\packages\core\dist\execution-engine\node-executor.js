"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeExecutor = void 0;
const p_cancelable_1 = require("p-cancelable");
const workflow_node_context_1 = require("./node-execution-context/workflow-node-context");
const node_execution_functions_1 = require("../node-execution-functions");
const workflow_state_manager_1 = require("./workflow-state-manager");
const data_manager_1 = require("./data-manager");
class NodeExecutor {
    constructor(workflow, stateManager, abortSignal) {
        this.workflow = workflow;
        this.stateManager = stateManager;
        this.abortSignal = abortSignal;
    }
    executeNode(node_1, inputData_1, context_1) {
        return __awaiter(this, arguments, void 0, function* (node, inputData, context, options = {}) {
            var _a, _b;
            // Check if execution was aborted
            if ((_a = this.abortSignal) === null || _a === void 0 ? void 0 : _a.aborted) {
                throw new p_cancelable_1.CancelError('Node execution was canceled.');
            }
            // Set node status to running
            this.stateManager.setNodeStatus(node.id, workflow_state_manager_1.NodeExecutionStatus.RUNNING);
            // Get retry settings from node configuration or options
            // Use type assertion to access extended properties safely
            const nodeRetrySettings = node.retryOnFail;
            const retrySettings = options.retrySettings || nodeRetrySettings;
            const maxRetries = (retrySettings === null || retrySettings === void 0 ? void 0 : retrySettings.count) || options.retries || 0;
            let lastError = null;
            // Retry logic
            for (let attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    // Check for abort between retries
                    if ((_b = this.abortSignal) === null || _b === void 0 ? void 0 : _b.aborted) {
                        throw new p_cancelable_1.CancelError('Node execution was canceled.');
                    }
                    const result = yield this.attemptNodeExecution(node, inputData, context, options);
                    // Mark as completed and store output
                    this.stateManager.setNodeOutput(node.id, result);
                    return result;
                }
                catch (error) {
                    lastError = error;
                    // If it's a cancellation, don't retry
                    if (error instanceof p_cancelable_1.CancelError) {
                        this.stateManager.setNodeError(node.id, error);
                        throw error;
                    }
                    // If this is the last attempt or retries are disabled, handle the error
                    if (attempt === maxRetries) {
                        return this.handleNodeError(node, error, options);
                    }
                    // Wait before retry with configurable backoff
                    if (attempt < maxRetries) {
                        const delay = this.calculateRetryDelay(attempt, retrySettings);
                        yield this.delay(delay);
                    }
                }
            }
            // This should never be reached, but handle it just in case
            if (lastError) {
                return this.handleNodeError(node, lastError, options);
            }
            throw new Error('Unexpected error in node execution retry logic');
        });
    }
    attemptNodeExecution(node, inputData, context, options) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            // Get the node type implementation
            const nodeType = (_a = this.workflow.nodeTypes) === null || _a === void 0 ? void 0 : _a.getByNameAndVersion(node.type, node.typeVersion || 1);
            if (!nodeType) {
                throw new Error(`Node type "${node.type}" not found.`);
            }
            // Create execution context for this node
            const nodeContext = new workflow_node_context_1.WorkflowNodeContext(this.workflow, node, context.getAll());
            const executeFunctions = new node_execution_functions_1.NodeExecuteFunctions(nodeContext, node, inputData);
            // Execute with timeout if specified
            const timeout = options.timeout || node.parameters.timeout;
            let executionPromise;
            if (!nodeType.execute) {
                throw new Error(`Node type "${node.type}" does not have an execute method.`);
            }
            executionPromise = nodeType.execute.call(executeFunctions);
            // Apply timeout if specified
            if (timeout && timeout > 0) {
                executionPromise = this.executeWithTimeout(executionPromise, timeout);
            }
            const result = yield executionPromise;
            // Normalize result to always be INodeExecutionData[]
            if (Array.isArray(result) && Array.isArray(result[0])) {
                return result.flat();
            }
            return result;
        });
    }
    handleNodeError(node, error, options) {
        var _a, _b;
        // Mark node as failed
        this.stateManager.setNodeError(node.id, error);
        // Check if we should continue on fail
        const continueOnFail = (_b = (_a = options.continueOnFail) !== null && _a !== void 0 ? _a : node.continueOnFail) !== null && _b !== void 0 ? _b : false;
        if (continueOnFail) {
            // Return error data instead of throwing
            const errorData = [{
                    json: {
                        error: error.message,
                        stack: error.stack,
                        nodeId: node.id,
                        nodeName: node.name,
                    },
                    pairedItem: { item: 0 },
                }];
            // Store error output in state manager
            this.stateManager.setNodeOutput(node.id, errorData);
            return errorData;
        }
        // Re-throw the error if not continuing on fail
        throw error;
    }
    executeWithTimeout(promise, timeoutMs) {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error(`Node execution timed out after ${timeoutMs}ms`));
            }, timeoutMs);
            promise
                .then(result => {
                clearTimeout(timeoutId);
                resolve(result);
            })
                .catch(error => {
                clearTimeout(timeoutId);
                reject(error);
            });
        });
    }
    calculateRetryDelay(attempt, retrySettings) {
        const baseDelay = (retrySettings === null || retrySettings === void 0 ? void 0 : retrySettings.delay) || 1000; // Default 1 second
        const backoffType = (retrySettings === null || retrySettings === void 0 ? void 0 : retrySettings.backoff) || 'exponential';
        switch (backoffType) {
            case 'linear':
                return baseDelay * (attempt + 1);
            case 'exponential':
            default:
                return baseDelay * Math.pow(2, attempt);
        }
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    // Static method to apply paired items using DataManager (enhanced version)
    static applyPairedItem(inputData, outputData) {
        return data_manager_1.DataManager.applyPairedItem(inputData, outputData);
    }
}
exports.NodeExecutor = NodeExecutor;
//# sourceMappingURL=node-executor.js.map