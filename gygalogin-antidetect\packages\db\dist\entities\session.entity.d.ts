import { ProfileEntity } from './profile.entity';
import { SessionState } from '@gygalogin/shared';
export declare class SessionEntity {
    id: string;
    profileId: string;
    profile: ProfileEntity;
    browserInstanceId?: string;
    state: SessionState;
    cookies?: Array<{
        name: string;
        value: string;
        domain: string;
        path: string;
        secure: boolean;
        httpOnly: boolean;
        sameSite?: 'Strict' | 'Lax' | 'None';
        expires?: string;
        maxAge?: number;
    }>;
    localStorage?: Record<string, string>;
    sessionStorage?: Record<string, string>;
    indexedDB?: {
        databases: Array<{
            name: string;
            version: number;
            objectStoreNames: string[];
        }>;
        data: Record<string, any>;
        metadata: {
            collectedAt: string;
            totalDatabases: number;
            totalSize: number;
            error?: string;
        };
    };
    cacheStorage?: {
        caches: Array<{
            name: string;
            entryCount: number;
            size: number;
        }>;
        data: Record<string, any>;
        metadata: {
            collectedAt: string;
            totalCaches: number;
            totalEntries: number;
            totalSize: number;
            error?: string;
        };
    };
    webSQL?: {
        databases: Array<{
            name: string;
            version: string;
            displayName: string;
            estimatedSize: number;
            tables: Array<{
                name: string;
                sql: string;
                recordCount: number;
            }>;
        }>;
        data: Record<string, any>;
        metadata: {
            collectedAt: string;
            totalDatabases: number;
            totalTables: number;
            totalRecords: number;
            totalSize: number;
            error?: string;
        };
    };
    currentPage?: {
        url: string;
        title: string;
        timestamp: string;
    };
    navigationHistory?: Array<{
        id: string;
        url: string;
        title: string;
        timestamp: string;
        referrer?: string;
        method: 'navigate' | 'reload' | 'back' | 'forward';
    }>;
    expiresAt?: Date;
    lastActivity?: Date;
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
}
//# sourceMappingURL=session.entity.d.ts.map