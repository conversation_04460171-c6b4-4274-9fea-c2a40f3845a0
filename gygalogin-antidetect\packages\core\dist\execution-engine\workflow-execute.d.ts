import { ExecutionRepository, WorkflowRepository } from '@gygalogin/db';
import { INode, INodeExecutionData, Workflow, WorkflowExecuteMode } from '@gygalogin/shared';
import EventEmitter from 'eventemitter3';
import PCancelable from 'p-cancelable';
import { ExecutionLifecycleHooks } from './execution-lifecycle-hooks';
import { <PERSON>raph<PERSON>un<PERSON> } from './graph-runner';
import { ExecuteContext } from './node-execution-context/execute-context';
import { NodeExecutor } from './node-executor';
import { WorkflowStateManager } from './workflow-state-manager';
export type WorkflowExecuteEvents = {
    beforeWorkflowStart: [];
    afterWorkflowEnd: [];
    beforeNodeExecute: [INode];
    afterNodeExecute: [INode, INodeExecutionData[]];
    onNodeError: [INode, Error];
};
export declare class WorkflowExecute {
    private workflowRepo?;
    private executionRepo?;
    /**
     * Factory method to create WorkflowExecute instance with database integration
     */
    static createFromDatabase(workflowId: string, workflowRepo: WorkflowRepository, executionRepo: ExecutionRepository, nodeTypes: any): Promise<WorkflowExecute>;
    private readonly emitter;
    on: <T extends keyof WorkflowExecuteEvents>(event: T, fn: (...args: EventEmitter.ArgumentMap<WorkflowExecuteEvents>[Extract<T, keyof WorkflowExecuteEvents>]) => void, context?: any) => EventEmitter<WorkflowExecuteEvents, any>;
    once: <T extends keyof WorkflowExecuteEvents>(event: T, fn: (...args: EventEmitter.ArgumentMap<WorkflowExecuteEvents>[Extract<T, keyof WorkflowExecuteEvents>]) => void, context?: any) => EventEmitter<WorkflowExecuteEvents, any>;
    off: <T extends keyof WorkflowExecuteEvents>(event: T, fn?: ((...args: EventEmitter.ArgumentMap<WorkflowExecuteEvents>[Extract<T, keyof WorkflowExecuteEvents>]) => void) | undefined, context?: any, once?: boolean) => EventEmitter<WorkflowExecuteEvents, any>;
    emit: <T extends keyof WorkflowExecuteEvents>(event: T, ...args: EventEmitter.ArgumentMap<WorkflowExecuteEvents>[Extract<T, keyof WorkflowExecuteEvents>]) => boolean;
    hooks: ExecutionLifecycleHooks;
    private workflow;
    private controller;
    private multiInputNodes;
    private waitingNodes;
    private currentExecutionId?;
    constructor(workflow: Workflow, workflowRepo?: WorkflowRepository | undefined, executionRepo?: ExecutionRepository | undefined);
    cancel(): void;
    run(): PCancelable<void>;
    applyPairedItem(inputData: INodeExecutionData[], outputData: INodeExecutionData[]): INodeExecutionData[];
    private precomputeMultiInputNodes;
    private processNodeOutput;
    execute(_mode: WorkflowExecuteMode): Promise<void>;
    executeNodeById(nodeId: string, inputData: INodeExecutionData[]): Promise<INodeExecutionData[][]>;
    executeSubgraph(startNodeId: string, inputData: INodeExecutionData[]): Promise<void>;
    hasCycles(): boolean;
    filterDisabledNodes(): INode[];
    runWithStateManager(runMode?: WorkflowExecuteMode): PCancelable<WorkflowStateManager>;
    createNodeExecutor(stateManager: WorkflowStateManager, abortSignal?: AbortSignal): NodeExecutor;
    createExecutionContext(executionId?: string, runMode?: WorkflowExecuteMode): ExecuteContext;
    createGraphRunner(stateManager: WorkflowStateManager, context: ExecuteContext, abortSignal?: AbortSignal): GraphRunner;
    private executeNode;
}
//# sourceMappingURL=workflow-execute.d.ts.map