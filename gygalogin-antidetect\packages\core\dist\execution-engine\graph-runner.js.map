{"version": 3, "file": "graph-runner.js", "sourceRoot": "", "sources": ["../../src/execution-engine/graph-runner.ts"], "names": [], "mappings": ";AAAA,uDAAuD;;;;;;;;;;;;AAEvD,+CAA2C;AAG3C,qEAAqF;AACrF,mDAAqE;AASrE,MAAa,WAAW;IAKtB,YACU,QAAkB,EAClB,YAAkC,EAClC,OAAuB,EACvB,KAA8B,EAC9B,WAAyB;QAJzB,aAAQ,GAAR,QAAQ,CAAU;QAClB,iBAAY,GAAZ,YAAY,CAAsB;QAClC,YAAO,GAAP,OAAO,CAAgB;QACvB,UAAK,GAAL,KAAK,CAAyB;QAC9B,gBAAW,GAAX,WAAW,CAAc;QAT3B,mBAAc,GAA0B,EAAE,CAAC;QAC3C,oBAAe,GAAgB,IAAI,GAAG,EAAE,CAAC;QAU/C,IAAI,CAAC,YAAY,GAAG,IAAI,4BAAY,CAAC,QAAQ,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;QAC1E,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAEY,GAAG;;;YACd,6CAA6C;YAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;YAChD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACjD,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAE/B,2BAA2B;YAC3B,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YAEnC,IAAI,CAAC;gBACH,0BAA0B;gBAC1B,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtC,yBAAyB;oBACzB,IAAI,MAAA,IAAI,CAAC,WAAW,0CAAE,OAAO,EAAE,CAAC;wBAC9B,MAAM,IAAI,0BAAW,CAAC,kCAAkC,CAAC,CAAC;oBAC5D,CAAC;oBAED,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;oBAC9C,IAAI,CAAC,SAAS;wBAAE,SAAS;oBAEzB,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;gBACzC,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAE9C,CAAC;oBAAS,CAAC;gBACT,yBAAyB;gBACzB,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YACnC,CAAC;QACH,CAAC;KAAA;IAEa,gBAAgB,CAAC,SAA8B;;YAC3D,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC;YAE/C,IAAI,CAAC;gBACH,+BAA+B;gBAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;gBAEhD,mBAAmB;gBACnB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBAE/F,8BAA8B;gBAC9B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;gBAE3D,oCAAoC;gBACpC,MAAM,yBAAyB,GAAG,4BAAY,CAAC,eAAe,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAEtF,gEAAgE;gBAChE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,yBAAyB,CAAC,CAAC;gBAE/D,0DAA0D;gBAC1D,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,EAAE,yBAAyB,CAAC,CAAC;YAEnE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,KAAK,YAAY,0BAAW,EAAE,CAAC;oBACjC,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,iBAAiB;gBACjB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,IAAI,EAAE,KAAc,CAAC,CAAC,CAAC;gBAE1D,0CAA0C;gBAC1C,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAc,EAAE,SAAS,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;KAAA;IAEa,eAAe,CAAC,IAAW,EAAE,KAAY,EAAE,SAA+B;;YACtF,OAAO,CAAC,KAAK,CAAC,wBAAwB,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAExE,qCAAqC;YACrC,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;YAEjE,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,oCAAoC;gBACpC,MAAM,SAAS,GAAyB,CAAC;wBACvC,IAAI,EAAE;4BACJ,KAAK,EAAE,KAAK,CAAC,OAAO;4BACpB,KAAK,EAAE,KAAK,CAAC,KAAK;4BAClB,YAAY,EAAE,IAAI,CAAC,EAAE;4BACrB,cAAc,EAAE,IAAI,CAAC,IAAI;4BACzB,iBAAiB,EAAE,SAAS;yBAC7B;wBACD,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;qBACxB,CAAC,CAAC;gBAEH,qCAAqC;gBACrC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;oBACnC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACxC,CAAC;YACH,CAAC;iBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,KAAK,MAAM,EAAE,CAAC;gBAC5D,mEAAmE;gBACnE,MAAM,KAAK,CAAC;YACd,CAAC;YACD,iEAAiE;QACnE,CAAC;KAAA;IAEa,iBAAiB,CAAC,YAAoB,EAAE,UAAgC;;YACpF,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,KAAK,YAAY,CAAC,CAAC;YAE3F,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;gBAC/B,8CAA8C;gBAC9C,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE,CAAC;oBAClC,SAAS;gBACX,CAAC;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC1D,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,YAAY,kCAAkC,YAAY,EAAE,CAAC,CAAC;oBAC/F,SAAS;gBACX,CAAC;gBAED,qCAAqC;gBACrC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;oBAC3C,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;oBACtC,SAAS;gBACX,CAAC;gBAED,iDAAiD;gBACjD,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;KAAA;IAEa,oBAAoB,CAChC,IAAW,EACX,SAAiB,EACjB,IAA0B;;YAE1B,oCAAoC;YACpC,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW;iBAC7C,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,KAAK,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,YAAY,KAAK,OAAO,CAAC;iBACrE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;YAE3B,8CAA8C;YAC9C,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC9C,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;YACnE,CAAC;YAED,iCAAiC;YACjC,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAEnF,qDAAqD;YACrD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC1E,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC7C,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;KAAA;IAEO,UAAU,CAAC,IAAW,EAAE,SAA+B,EAAE,OAA8B;QAC7F,sBAAsB;QACtB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,4CAAmB,CAAC,OAAO,CAAC,CAAC;YACtE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACvB,IAAI;YACJ,SAAS;YACT,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAEO,yBAAyB;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAC7C,MAAM,WAAW,GAA2B,EAAE,CAAC;QAE/C,0BAA0B;QAC1B,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC5B,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QAC3B,CAAC;QAED,2DAA2D;QAC3D,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC7C,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,SAAS,EAAE,CAAC;gBAClF,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YACnC,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE,CAAC;YACjC,IAAI,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;IACH,CAAC;IAED,kBAAkB;IACX,iBAAiB;QACtB,OAAO,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;IAEM,cAAc;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;IACpC,CAAC;IAEM,UAAU;QACf,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAC3B,CAAC;IAEM,YAAY;QACjB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED,uEAAuE;IAChE,WAAW,CAAC,IAAW,EAAE,SAA+B,EAAE,OAA8B;QAC7F,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED,kDAAkD;IAC3C,kBAAkB;QACvB,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACvC,CAAC;CACF;AAvOD,kCAuOC"}