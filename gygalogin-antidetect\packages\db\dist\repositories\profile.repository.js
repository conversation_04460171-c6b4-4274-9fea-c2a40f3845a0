"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfileRepository = void 0;
const decorators_1 = require("@gygalogin/decorators");
const typeorm_1 = require("typeorm");
const profile_entity_1 = require("../entities/profile.entity");
let ProfileRepository = class ProfileRepository extends typeorm_1.Repository {
    constructor(dataSource) {
        super(profile_entity_1.ProfileEntity, dataSource.manager);
    }
    /**
     * Find entity by ID - added for compatibility with core package
     */
    async findById(id) {
        return this.findOne({ where: { id } });
    }
    /**
     * Create a new profile entity and return it
     */
    async createProfile(profileData) {
        const profile = this.create(profileData);
        return this.save(profile);
    }
    /**
     * Update profile and return the updated entity
     */
    async updateProfile(id, updateData) {
        await this.update(id, updateData);
        return this.findOneBy({ id });
    }
    /**
     * Delete profile by id and return success status
     */
    async deleteProfile(id) {
        const result = await this.delete(id);
        return (result.affected ?? 0) > 0;
    }
    /**
     * Find profiles by name (case-insensitive partial match)
     */
    async findByName(name) {
        return this.find({
            where: {
                name: (0, typeorm_1.Like)(`%${name}%`),
            },
        });
    }
    /**
     * Search profiles by name (alias for findByName for test compatibility)
     */
    async searchByName(name) {
        return this.findByName(name);
    }
    /**
     * Find active profiles
     */
    async findActive() {
        return this.findBy({ isActive: true });
    }
    /**
     * Find profiles by tags
     */
    async findByTags(tags) {
        const queryBuilder = this.createQueryBuilder('profile');
        tags.forEach((tag, index) => {
            queryBuilder.orWhere(`profile.tags LIKE :tag${index}`, { [`tag${index}`]: `%${tag}%` });
        });
        return queryBuilder.getMany();
    }
    /**
     * Search profiles with multiple criteria
     */
    async search(criteria, options) {
        const queryBuilder = this.createQueryBuilder('profile');
        if (criteria.name) {
            queryBuilder.andWhere('profile.name LIKE :name', { name: `%${criteria.name}%` });
        }
        if (criteria.isActive !== undefined) {
            queryBuilder.andWhere('profile.isActive = :isActive', { isActive: criteria.isActive });
        }
        if (criteria.createdAfter) {
            queryBuilder.andWhere('profile.createdAt >= :createdAfter', {
                createdAfter: criteria.createdAfter,
            });
        }
        if (criteria.createdBefore) {
            queryBuilder.andWhere('profile.createdAt <= :createdBefore', {
                createdBefore: criteria.createdBefore,
            });
        }
        if (criteria.hasProxy !== undefined) {
            if (criteria.hasProxy) {
                queryBuilder.andWhere('profile.proxyId IS NOT NULL');
            }
            else {
                queryBuilder.andWhere('profile.proxyId IS NULL');
            }
        }
        if (criteria.tags && criteria.tags.length > 0) {
            const tagConditions = criteria.tags.map((_, index) => `profile.tags LIKE :tag${index}`);
            const tagParams = criteria.tags.reduce((params, tag, index) => {
                params[`tag${index}`] = `%${tag}%`;
                return params;
            }, {});
            queryBuilder.andWhere(`(${tagConditions.join(' OR ')})`, tagParams);
        }
        if (options?.take) {
            queryBuilder.take(options.take);
        }
        if (options?.skip) {
            queryBuilder.skip(options.skip);
        }
        if (options?.order) {
            Object.entries(options.order).forEach(([key, direction]) => {
                queryBuilder.addOrderBy(`profile.${key}`, direction);
            });
        }
        return queryBuilder.getMany();
    }
    /**
     * Find a single profile with its proxy data
     */
    async findWithProxyById(id) {
        return this.createQueryBuilder('profile')
            .leftJoinAndSelect('profile.proxy', 'proxy')
            .where('profile.id = :id', { id })
            .getOne();
    }
    /**
     * Find all profiles with their proxy data
     */
    async findWithProxy() {
        return this.createQueryBuilder('profile').leftJoinAndSelect('profile.proxy', 'proxy').getMany();
    }
    /**
     * Find a single profile with its sessions
     */
    async findWithSessionsById(id) {
        return this.createQueryBuilder('profile')
            .leftJoinAndSelect('profile.sessions', 'session')
            .where('profile.id = :id', { id })
            .orderBy('session.createdAt', 'DESC')
            .getOne();
    }
    /**
     * Find all profiles with their sessions
     */
    async findWithSessions() {
        return this.createQueryBuilder('profile')
            .leftJoinAndSelect('profile.sessions', 'session')
            .orderBy('session.createdAt', 'DESC')
            .getMany();
    }
    /**
     * Update last used timestamp
     */
    async updateLastUsed(id) {
        await this.update(id, { lastUsed: new Date() });
    }
    /**
     * Get profiles usage statistics
     */
    async getUsageStats() {
        const total = await this.count();
        const active = await this.countBy({ isActive: true });
        const inactive = total - active;
        const withProxyCount = await this.createQueryBuilder('profile')
            .where('profile.proxyId IS NOT NULL')
            .getCount();
        const withoutProxy = total - withProxyCount;
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        const recentlyUsed = await this.createQueryBuilder('profile')
            .where('profile.lastUsed >= :sevenDaysAgo', { sevenDaysAgo })
            .getCount();
        return {
            total,
            active,
            inactive,
            withProxy: withProxyCount,
            withoutProxy,
            recentlyUsed,
        };
    }
};
exports.ProfileRepository = ProfileRepository;
exports.ProfileRepository = ProfileRepository = __decorate([
    (0, decorators_1.Service)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], ProfileRepository);
//# sourceMappingURL=profile.repository.js.map