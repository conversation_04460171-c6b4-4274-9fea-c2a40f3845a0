{"version": 3, "file": "error-recovery-manager.d.ts", "sourceRoot": "", "sources": ["../../../src/execution-engine/error-recovery/error-recovery-manager.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,KAAK,EAAE,MAAM,mBAAmB,CAAC;AAC1C,OAAO,EACL,sBAAsB,EACtB,oBAAoB,EACpB,mBAAmB,EAQpB,MAAM,kBAAkB,CAAC;AAE1B;;GAEG;AACH,MAAM,WAAW,mBAAmB;IAClC,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,iBAAiB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC7B,kBAAkB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC9B,gBAAgB,CAAC,EAAE,sBAAsB,EAAE,CAAC;IAC5C,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,mBAAmB,CAAC,EAAE,OAAO,CAAC;CAC/B;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,aAAa,EAAE,MAAM,CAAC;IACtB,oBAAoB,EAAE,MAAM,CAAC;IAC7B,gBAAgB,EAAE,MAAM,CAAC;IACzB,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACvC,iBAAiB,EAAE,MAAM,CAAC;IAC1B,mBAAmB,EAAE,MAAM,CAAC;CAC7B;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,IAAI,EAAE,kBAAkB,GAAG,oBAAoB,GAAG,iBAAiB,GAAG,kBAAkB,CAAC;IACzF,SAAS,EAAE,IAAI,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,KAAK,CAAC;IACb,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,mBAAmB,CAAC;IAC7B,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB;AAED;;GAEG;AACH,qBAAa,oBAAoB;IAYnB,OAAO,CAAC,MAAM;IAX1B,OAAO,CAAC,UAAU,CAAgC;IAClD,OAAO,CAAC,KAAK,CAOX;IACF,OAAO,CAAC,cAAc,CAA+C;gBAEjD,MAAM,GAAE,mBAAwB;IAKpD;;OAEG;IACI,eAAe,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,kBAAkB,KAAK,IAAI,GAAG,IAAI;IAI3E;;OAEG;IACI,2BAA2B,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,kBAAkB,KAAK,IAAI,GAAG,IAAI;IAOvF;;OAEG;IACU,eAAe,CAC1B,KAAK,EAAE,KAAK,EACZ,IAAI,EAAE,KAAK,EACX,OAAO,EAAE,OAAO,CAAC,oBAAoB,CAAC,GACrC,OAAO,CAAC,mBAAmB,CAAC;IAuH/B;;OAEG;IACI,WAAW,CAAC,QAAQ,EAAE,sBAAsB,GAAG,IAAI;IAS1D;;OAEG;IACI,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAM5C;;OAEG;IACI,aAAa,IAAI,sBAAsB,EAAE;IAIhD;;OAEG;IACI,QAAQ,IAAI,kBAAkB;IAIrC;;OAEG;IACI,UAAU,IAAI,IAAI;IAWzB;;OAEG;IACI,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,mBAAmB,CAAC,GAAG,IAAI;IAI/D,OAAO,CAAC,2BAA2B;IAcnC,OAAO,CAAC,mBAAmB;IAM3B,OAAO,CAAC,iBAAiB;IAezB,OAAO,CAAC,oBAAoB;IAoC5B,OAAO,CAAC,SAAS;CASlB"}