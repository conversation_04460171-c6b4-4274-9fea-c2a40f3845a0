{"version": 3, "file": "workflow-state-manager.d.ts", "sourceRoot": "", "sources": ["../../src/execution-engine/workflow-state-manager.ts"], "names": [], "mappings": "AAEA,OAAO,EAAS,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAE9D,oBAAY,mBAAmB;IAC7B,OAAO,YAAY;IACnB,OAAO,YAAY;IACnB,SAAS,cAAc;IACvB,MAAM,WAAW;IACjB,OAAO,YAAY;CACpB;AAED,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,mBAAmB,CAAC;IAC5B,MAAM,CAAC,EAAE,kBAAkB,EAAE,CAAC;IAC9B,KAAK,CAAC,EAAE,KAAK,CAAC;IACd,SAAS,CAAC,EAAE,IAAI,CAAC;IACjB,OAAO,CAAC,EAAE,IAAI,CAAC;CAChB;AAED,MAAM,WAAW,iBAAiB;IAChC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAC/C,cAAc,EAAE,MAAM,EAAE,CAAC;CAC1B;AAED,qBAAa,oBAAoB;IAMnB,OAAO,CAAC,UAAU;IAAU,OAAO,CAAC,WAAW;IAL3D,OAAO,CAAC,UAAU,CAAsC;IACxD,OAAO,CAAC,YAAY,CAA6C;IACjE,OAAO,CAAC,kBAAkB,CAAC,CAAO;IAClC,OAAO,CAAC,gBAAgB,CAAC,CAAO;gBAEZ,UAAU,EAAE,MAAM,EAAU,WAAW,EAAE,MAAM;IAG5D,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,mBAAmB,GAAG,IAAI;IAehE,aAAa,CAAC,MAAM,EAAE,MAAM,GAAG,mBAAmB;IAIlD,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,UAAU,GAAG,SAAS;IAIpD,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,kBAAkB,EAAE,GAAG,IAAI;IAQjE,aAAa,CAAC,MAAM,EAAE,MAAM,GAAG,kBAAkB,EAAE,GAAG,SAAS;IAI/D,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI;IAShD,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,GAAG,IAAI;IAOrE,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,kBAAkB,EAAE,GAAG,OAAO;IAgBpF,oBAAoB,CAAC,MAAM,EAAE,MAAM,GAAG,kBAAkB,EAAE;IAU1D,iBAAiB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAIvC,aAAa,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO;IAKtC,cAAc,IAAI,IAAI;IAItB,YAAY,IAAI,IAAI;IAIpB,oBAAoB,IAAI,MAAM,GAAG,SAAS;IAS1C,iBAAiB,IAAI,MAAM,EAAE;IAU7B,cAAc,IAAI,MAAM,EAAE;IAU1B,eAAe,IAAI,MAAM,EAAE;IAU3B,gBAAgB,IAAI,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC;IAI3C,mBAAmB,IAAI,OAAO;IAW9B,SAAS,IAAI,OAAO;IAKpB,mBAAmB,IAAI,GAAG;CAelC"}