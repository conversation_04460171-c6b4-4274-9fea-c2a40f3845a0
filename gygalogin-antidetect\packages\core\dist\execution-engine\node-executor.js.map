{"version": 3, "file": "node-executor.js", "sourceRoot": "", "sources": ["../../src/execution-engine/node-executor.ts"], "names": [], "mappings": ";AAAA,uDAAuD;;;;;;;;;;;;AAEvD,+CAA2C;AAG3C,0FAAqF;AACrF,0EAAmE;AACnE,qEAAqF;AACrF,iDAA6C;AA4B7C,MAAa,YAAY;IACvB,YACU,QAAkB,EAClB,YAAkC,EAClC,WAAyB;QAFzB,aAAQ,GAAR,QAAQ,CAAU;QAClB,iBAAY,GAAZ,YAAY,CAAsB;QAClC,gBAAW,GAAX,WAAW,CAAc;IAChC,CAAC;IAES,WAAW;6DACtB,IAAW,EACX,SAA+B,EAC/B,OAAuB,EACvB,UAAgC,EAAE;;YAElC,iCAAiC;YACjC,IAAI,MAAA,IAAI,CAAC,WAAW,0CAAE,OAAO,EAAE,CAAC;gBAC9B,MAAM,IAAI,0BAAW,CAAC,8BAA8B,CAAC,CAAC;YACxD,CAAC;YAED,6BAA6B;YAC7B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,4CAAmB,CAAC,OAAO,CAAC,CAAC;YAEtE,wDAAwD;YACxD,0DAA0D;YAC1D,MAAM,iBAAiB,GAAI,IAAY,CAAC,WAAyC,CAAC;YAClF,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,iBAAiB,CAAC;YACjE,MAAM,UAAU,GAAG,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,KAAK,KAAI,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;YAChE,IAAI,SAAS,GAAiB,IAAI,CAAC;YAEnC,cAAc;YACd,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;gBACvD,IAAI,CAAC;oBACH,kCAAkC;oBAClC,IAAI,MAAA,IAAI,CAAC,WAAW,0CAAE,OAAO,EAAE,CAAC;wBAC9B,MAAM,IAAI,0BAAW,CAAC,8BAA8B,CAAC,CAAC;oBACxD,CAAC;oBAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;oBAElF,qCAAqC;oBACrC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;oBAEjD,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,SAAS,GAAG,KAAc,CAAC;oBAE3B,sCAAsC;oBACtC,IAAI,KAAK,YAAY,0BAAW,EAAE,CAAC;wBACjC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;wBAC/C,MAAM,KAAK,CAAC;oBACd,CAAC;oBAED,wEAAwE;oBACxE,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC;wBAC3B,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAc,EAAE,OAAO,CAAC,CAAC;oBAC7D,CAAC;oBAED,8CAA8C;oBAC9C,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;wBACzB,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;wBAC/D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC1B,CAAC;gBACH,CAAC;YACH,CAAC;YAED,2DAA2D;YAC3D,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;KAAA;IAEa,oBAAoB,CAChC,IAAW,EACX,SAA+B,EAC/B,OAAuB,EACvB,OAA6B;;;YAE7B,mCAAmC;YACnC,MAAM,QAAQ,GAAG,MAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,0CAAE,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;YAChG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,cAAc,IAAI,CAAC,IAAI,cAAc,CAAC,CAAC;YACzD,CAAC;YAED,yCAAyC;YACzC,MAAM,WAAW,GAAG,IAAI,2CAAmB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YACnF,MAAM,gBAAgB,GAAG,IAAI,+CAAoB,CAAC,WAAW,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YAEhF,oCAAoC;YACpC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YAC3D,IAAI,gBAAwE,CAAC;YAE7E,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,cAAc,IAAI,CAAC,IAAI,oCAAoC,CAAC,CAAC;YAC/E,CAAC;YAED,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAE3D,6BAA6B;YAC7B,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;gBAC3B,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;YACxE,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC;YAEtC,qDAAqD;YACrD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtD,OAAQ,MAAiC,CAAC,IAAI,EAAE,CAAC;YACnD,CAAC;YAED,OAAO,MAA8B,CAAC;QACxC,CAAC;KAAA;IAEO,eAAe,CACrB,IAAW,EACX,KAAY,EACZ,OAA6B;;QAE7B,sBAAsB;QACtB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAE/C,sCAAsC;QACtC,MAAM,cAAc,GAAG,MAAA,MAAA,OAAO,CAAC,cAAc,mCAAI,IAAI,CAAC,cAAc,mCAAI,KAAK,CAAC;QAE9E,IAAI,cAAc,EAAE,CAAC;YACnB,wCAAwC;YACxC,MAAM,SAAS,GAAyB,CAAC;oBACvC,IAAI,EAAE;wBACJ,KAAK,EAAE,KAAK,CAAC,OAAO;wBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;wBAClB,MAAM,EAAE,IAAI,CAAC,EAAE;wBACf,QAAQ,EAAE,IAAI,CAAC,IAAI;qBACpB;oBACD,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;iBACxB,CAAC,CAAC;YAEH,sCAAsC;YACtC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;YAEpD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,+CAA+C;QAC/C,MAAM,KAAK,CAAC;IACd,CAAC;IAEO,kBAAkB,CACxB,OAAmB,EACnB,SAAiB;QAEjB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAChC,MAAM,CAAC,IAAI,KAAK,CAAC,kCAAkC,SAAS,IAAI,CAAC,CAAC,CAAC;YACrE,CAAC,EAAE,SAAS,CAAC,CAAC;YAEd,OAAO;iBACJ,IAAI,CAAC,MAAM,CAAC,EAAE;gBACb,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE;gBACb,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB,CAAC,OAAe,EAAE,aAA8B;QACzE,MAAM,SAAS,GAAG,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,KAAK,KAAI,IAAI,CAAC,CAAC,mBAAmB;QACnE,MAAM,WAAW,GAAG,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,KAAI,aAAa,CAAC;QAE5D,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,QAAQ;gBACX,OAAO,SAAS,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YACnC,KAAK,aAAa,CAAC;YACnB;gBACE,OAAO,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,2EAA2E;IACpE,MAAM,CAAC,eAAe,CAC3B,SAA+B,EAC/B,UAAgC;QAEhC,OAAO,0BAAW,CAAC,eAAe,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IAC5D,CAAC;CACF;AA/LD,oCA+LC"}