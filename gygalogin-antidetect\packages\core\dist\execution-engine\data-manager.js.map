{"version": 3, "file": "data-manager.js", "sourceRoot": "", "sources": ["../../src/execution-engine/data-manager.ts"], "names": [], "mappings": ";AAAA,uDAAuD;;;AA2BvD,MAAa,WAAW;IACtB;;OAEG;IACI,MAAM,CAAC,eAAe,CAC3B,SAA+B,EAC/B,UAAgC,EAChC,UAA8B,EAAE;QAEhC,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3C,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,SAAS,CAAC;QAEvC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAC1D,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;YACpF,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAC3D,KAAK,SAAS,CAAC;YACf;gBACE,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAChC,SAA+B,EAC/B,UAAgC;QAEhC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,iCAC3B,MAAM,KACT,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IACvB,CAAC,CAAC;QACN,CAAC;QAED,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAO,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,iCAC3B,MAAM,KACT,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IACvB,CAAC,CAAC;QACN,CAAC;QAED,IAAI,SAAS,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC;YAC3C,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,iCACpC,MAAM,KACT,UAAU,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAC3B,CAAC,CAAC;QACN,CAAC;QAED,OAAO,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,iCAC3B,MAAM,KACT,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IACvB,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,oBAAoB,CACjC,SAA+B,EAC/B,UAAgC;QAEhC,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,iCACpC,MAAM,KACT,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAC3D,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAClC,SAA+B,EAC/B,UAAgC,EAChC,eAAwB;QAExB,MAAM,SAAS,GAAG,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,CAAC,CAAC;QACvC,OAAO,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,iCAC3B,MAAM,KACT,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAC/D,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAClC,SAA+B,EAC/B,UAAgC;QAEhC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,iCAC3B,MAAM,KACT,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,IACvB,CAAC,CAAC;QACN,CAAC;QAED,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,iCACpC,MAAM,KACT,UAAU,EAAE,EAAE,IAAI,EAAE,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,IAC9C,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,SAAS,CACrB,UAAkC,EAClC,UAA6B,EAAE;QAE/B,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3C,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;QAED,MAAM,EAAE,OAAO,EAAE,QAAQ,GAAG,QAAQ,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QAEpE,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;YAC5D,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YAC1C,KAAK,QAAQ,CAAC;YACd;gBACE,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,cAAc,CAAC,UAAkC;QAC9D,MAAM,MAAM,GAAyB,EAAE,CAAC;QACxC,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;gBAC7B,MAAM,CAAC,IAAI,iCACN,IAAI,KACP,UAAU,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,IACjC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,eAAe,CAAC,UAAkC;QAC/D,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC;QACH,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,UAAU,CACvB,UAAkC,EAClC,OAAgB,EAChB,eAAyB,EAAE;QAE3B,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,GAAG,EAA2B,CAAC;QAElD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;gBAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAEpC,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACzB,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;oBACvC,yBAAyB;oBACzB,MAAM,UAAU,qBAAqB,QAAQ,CAAC,IAAI,CAAE,CAAC;oBAErD,gEAAgE;oBAChE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBACrD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;4BAChC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;wBAC1B,CAAC;oBACH,CAAC;oBAED,MAAM,CAAC,GAAG,CAAC,QAAQ,kCACd,IAAI,KACP,IAAI,EAAE,UAAU,EAChB,UAAU,EAAE,QAAQ,CAAC,UAAU,IAC/B,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,SAAS,CACrB,IAA0B,EAC1B,UAA6B,EAAE;QAE/B,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;QAEjE,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;QAED,IAAI,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,YAAY,CACzB,IAA0B,EAC1B,QAAgB,EAChB,iBAA0B;QAE1B,MAAM,MAAM,GAA2B,EAAE,CAAC;QAE1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC;YAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC;YAE1C,IAAI,iBAAiB,EAAE,CAAC;gBACtB,iCAAiC;gBACjC,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,iCAC7C,IAAI,KACP,UAAU,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAC3B,CAAC,CAAC;gBACJ,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,UAAU,CACvB,IAA0B,EAC1B,OAAe,EACf,iBAA0B;QAE1B,MAAM,MAAM,GAAG,IAAI,GAAG,EAA6B,CAAC;QAEpD,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;YACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEpC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC1B,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAC3B,CAAC;YAED,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,MAAM,GAA2B,EAAE,CAAC;QAE1C,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC;YACpC,IAAI,iBAAiB,EAAE,CAAC;gBACtB,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,iCAC7C,IAAI,KACP,UAAU,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAC3B,CAAC,CAAC;gBACJ,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,aAAa,CACzB,IAA0B,EAC1B,UAAiC,EAAE;QAEnC,IAAI,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;QAEvB,eAAe;QACf,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,MAAO,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QAC7E,CAAC;QAED,kBAAkB;QAClB,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,iCAChC,IAAI,KACP,IAAI,EAAE,OAAO,CAAC,SAAU,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAC1C,CAAC,CAAC;QACN,CAAC;QAED,aAAa;QACb,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,IAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACvD,CAAC;QAED,yCAAyC;QACzC,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,iCAC9B,IAAI,KACP,UAAU,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAC3B,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAC5B,cAA0D,EAC1D,QAAgB;QAEhB,OAAO,cAAc,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,mBAAmB,CAC/B,IAA0B,EAC1B,SAAiB;QAEjB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,YAAY,CAAC,IAA0B;QACnD,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACrC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;QACpC,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAErB,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;gBACpD,SAAS;YACX,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAChD,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,gDAAgD,CAAC,CAAC;YAClF,CAAC;YAED,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC;gBACzG,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,mCAAmC,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,eAAe;QAC3B,OAAO;YACL,IAAI,EAAE,EAAE;YACR,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;SACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,SAAS,CAAC,IAA0B;QAChD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACvB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzE,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,mBAAM,IAAI,CAAC,UAAU,EAAG,CAAC,CAAC,SAAS;SACjE,CAAC,CAAC,CAAC;IACN,CAAC;CACF;AAvZD,kCAuZC"}