import { INodeExecutionData } from '@gygalogin/shared';
export declare enum NodeExecutionStatus {
    PENDING = "pending",
    RUNNING = "running",
    COMPLETED = "completed",
    FAILED = "failed",
    SKIPPED = "skipped"
}
export interface INodeState {
    status: NodeExecutionStatus;
    output?: INodeExecutionData[];
    error?: Error;
    startTime?: Date;
    endTime?: Date;
}
export interface IWaitingNodeState {
    received: Record<string, INodeExecutionData[]>;
    requiredInputs: string[];
}
export declare class WorkflowStateManager {
    private workflowId;
    private executionId;
    private nodeStates;
    private waitingNodes;
    private executionStartTime?;
    private executionEndTime?;
    constructor(workflowId: string, executionId: string);
    setNodeStatus(nodeId: string, status: NodeExecutionStatus): void;
    getNodeStatus(nodeId: string): NodeExecutionStatus;
    getNodeState(nodeId: string): INodeState | undefined;
    setNodeOutput(nodeId: string, output: INodeExecutionData[]): void;
    getNodeOutput(nodeId: string): INodeExecutionData[] | undefined;
    setNodeError(nodeId: string, error: Error): void;
    initializeWaitingNode(nodeId: string, requiredInputs: string[]): void;
    addNodeInput(nodeId: string, inputName: string, data: INodeExecutionData[]): boolean;
    getWaitingNodeInputs(nodeId: string): INodeExecutionData[];
    removeWaitingNode(nodeId: string): void;
    isNodeWaiting(nodeId: string): boolean;
    startExecution(): void;
    endExecution(): void;
    getExecutionDuration(): number | undefined;
    getCompletedNodes(): string[];
    getFailedNodes(): string[];
    getRunningNodes(): string[];
    getAllNodeStates(): Map<string, INodeState>;
    isExecutionComplete(): boolean;
    hasErrors(): boolean;
    getExecutionSummary(): any;
}
//# sourceMappingURL=workflow-state-manager.d.ts.map