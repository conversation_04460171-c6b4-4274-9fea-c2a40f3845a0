import { INode } from '@gygalogin/shared';
import { IErrorRecoveryStrategy, ErrorRecoveryContext, ErrorRecoveryResult } from './error-recovery';
/**
 * Configuration for error recovery manager
 */
export interface ErrorRecoveryConfig {
    maxGlobalRetries?: number;
    maxRecoveryTime?: number;
    enabledStrategies?: string[];
    disabledStrategies?: string[];
    customStrategies?: IErrorRecoveryStrategy[];
    defaultRetryDelay?: number;
    logRecoveryAttempts?: boolean;
}
/**
 * Statistics about error recovery attempts
 */
export interface ErrorRecoveryStats {
    totalAttempts: number;
    successfulRecoveries: number;
    failedRecoveries: number;
    strategiesUsed: Record<string, number>;
    totalRecoveryTime: number;
    averageRecoveryTime: number;
}
/**
 * Event emitted during error recovery
 */
export interface ErrorRecoveryEvent {
    type: 'recovery_started' | 'recovery_completed' | 'recovery_failed' | 'strategy_applied';
    timestamp: Date;
    nodeId: string;
    error: Error;
    strategy?: string;
    result?: ErrorRecoveryResult;
    elapsedTime?: number;
}
/**
 * Manager for coordinating error recovery strategies
 */
export declare class ErrorRecoveryManager {
    private config;
    private strategies;
    private stats;
    private eventListeners;
    constructor(config?: ErrorRecoveryConfig);
    /**
     * Add an event listener for recovery events
     */
    onRecoveryEvent(listener: (event: ErrorRecoveryEvent) => void): void;
    /**
     * Remove an event listener
     */
    removeRecoveryEventListener(listener: (event: ErrorRecoveryEvent) => void): void;
    /**
     * Attempt to recover from an error using available strategies
     */
    attemptRecovery(error: Error, node: INode, context: Partial<ErrorRecoveryContext>): Promise<ErrorRecoveryResult>;
    /**
     * Add a custom recovery strategy
     */
    addStrategy(strategy: IErrorRecoveryStrategy): void;
    /**
     * Remove a recovery strategy by name
     */
    removeStrategy(name: string): boolean;
    /**
     * Get list of available strategies
     */
    getStrategies(): IErrorRecoveryStrategy[];
    /**
     * Get recovery statistics
     */
    getStats(): ErrorRecoveryStats;
    /**
     * Reset recovery statistics
     */
    resetStats(): void;
    /**
     * Update configuration
     */
    updateConfig(config: Partial<ErrorRecoveryConfig>): void;
    private initializeDefaultStrategies;
    private addCustomStrategies;
    private isStrategyEnabled;
    private handleRecoveryResult;
    private emitEvent;
}
//# sourceMappingURL=error-recovery-manager.d.ts.map