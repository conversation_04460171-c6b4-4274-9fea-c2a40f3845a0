{"version": 3, "file": "execution-errors.js", "sourceRoot": "", "sources": ["../../src/errors/execution-errors.ts"], "names": [], "mappings": ";AAAA,uDAAuD;;;AAEvD,8CAA8C;AAE9C;;GAEG;AACH,MAAa,kBAAmB,SAAQ,kBAAS;IAC/C,YACE,OAAe,EACC,MAAc,EACd,QAAiB,EACjB,OAA6B,EAC7B,OAAgB,EAChB,WAAoB;QAEpC,KAAK,CAAC,OAAO,EAAE,sBAAsB,EAAE,GAAG,CAAC,CAAC;QAN5B,WAAM,GAAN,MAAM,CAAQ;QACd,aAAQ,GAAR,QAAQ,CAAS;QACjB,YAAO,GAAP,OAAO,CAAsB;QAC7B,YAAO,GAAP,OAAO,CAAS;QAChB,gBAAW,GAAX,WAAW,CAAS;IAGtC,CAAC;CACF;AAXD,gDAWC;AAED;;GAEG;AACH,MAAa,gBAAiB,SAAQ,kBAAS;IAG7C,YACkB,MAAc,EACd,OAAe,EACf,QAAiB,EACjC,OAA6B,EACb,OAAgB;QAEhC,KAAK,CACH,kCAAkC,OAAO,IAAI,EAC7C,oBAAoB,EACpB,GAAG,CACJ,CAAC;QAVc,WAAM,GAAN,MAAM,CAAQ;QACd,YAAO,GAAP,OAAO,CAAQ;QACf,aAAQ,GAAR,QAAQ,CAAS;QAEjB,YAAO,GAAP,OAAO,CAAS;QAOhC,oCAAoC;QACpC,IAAI,CAAC,OAAO,mBAAK,OAAO,IAAK,OAAO,CAAE,CAAC;IACzC,CAAC;CACF;AAlBD,4CAkBC;AAED;;GAEG;AACH,MAAa,kBAAmB,SAAQ,kBAAS;IAC/C,YACkB,MAAc,EACd,aAAqB,EACrB,YAAqB,EACrB,WAAiB,EACjB,QAAiB;QAEjC,KAAK,CACH,sBAAsB,aAAa,IAAI,YAAY,CAAC,CAAC,CAAC,cAAc,YAAY,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAC1F,sBAAsB,EACtB,GAAG,CACJ,CAAC;QAVc,WAAM,GAAN,MAAM,CAAQ;QACd,kBAAa,GAAb,aAAa,CAAQ;QACrB,iBAAY,GAAZ,YAAY,CAAS;QACrB,gBAAW,GAAX,WAAW,CAAM;QACjB,aAAQ,GAAR,QAAQ,CAAS;IAOnC,CAAC;CACF;AAdD,gDAcC;AAED;;GAEG;AACH,MAAa,sBAAuB,SAAQ,kBAAS;IACnD,YACE,OAAe,EACC,WAAmB,EACnB,UAAkB,EAClB,KAA6C,EAC7C,OAA6B,EAC7B,YAAqB;QAErC,KAAK,CAAC,OAAO,EAAE,0BAA0B,EAAE,GAAG,CAAC,CAAC;QANhC,gBAAW,GAAX,WAAW,CAAQ;QACnB,eAAU,GAAV,UAAU,CAAQ;QAClB,UAAK,GAAL,KAAK,CAAwC;QAC7C,YAAO,GAAP,OAAO,CAAsB;QAC7B,iBAAY,GAAZ,YAAY,CAAS;IAGvC,CAAC;CACF;AAXD,wDAWC;AAED;;GAEG;AACH,MAAa,cAAe,SAAQ,kBAAS;IAC3C,YACE,OAAe,EACC,UAAmB,EACnB,UAAmB,EACnB,UAAmB,EACnB,OAA6B;QAE7C,KAAK,CAAC,OAAO,EAAE,iBAAiB,EAAE,GAAG,CAAC,CAAC;QALvB,eAAU,GAAV,UAAU,CAAS;QACnB,eAAU,GAAV,UAAU,CAAS;QACnB,eAAU,GAAV,UAAU,CAAS;QACnB,YAAO,GAAP,OAAO,CAAsB;IAG/C,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS;YAC7B,IAAI,CAAC,UAAU,KAAK,SAAS;YAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IAC3C,CAAC;CACF;AAhBD,wCAgBC;AAED;;GAEG;AACH,MAAa,mBAAoB,SAAQ,kBAAS;IAChD,YACE,OAAe,EACC,MAAc,EACd,SAAkB,EAClB,SAAkB,EAClB,cAAoB,EACpB,UAAgB;QAEhC,KAAK,CAAC,OAAO,EAAE,uBAAuB,EAAE,GAAG,CAAC,CAAC;QAN7B,WAAM,GAAN,MAAM,CAAQ;QACd,cAAS,GAAT,SAAS,CAAS;QAClB,cAAS,GAAT,SAAS,CAAS;QAClB,mBAAc,GAAd,cAAc,CAAM;QACpB,eAAU,GAAV,UAAU,CAAM;IAGlC,CAAC;CACF;AAXD,kDAWC;AAED;;GAEG;AACH,MAAa,eAAgB,SAAQ,kBAAS;IAC5C,YACE,OAAe,EACC,cAAsB,EACtB,MAAe,EACf,OAA6B;QAE7C,KAAK,CAAC,OAAO,EAAE,kBAAkB,EAAE,GAAG,CAAC,CAAC;QAJxB,mBAAc,GAAd,cAAc,CAAQ;QACtB,WAAM,GAAN,MAAM,CAAS;QACf,YAAO,GAAP,OAAO,CAAsB;IAG/C,CAAC;CACF;AATD,0CASC;AAED;;GAEG;AACH,MAAa,uBAAwB,SAAQ,kBAAS;IACpD,YACkB,OAAe,EACf,aAAqB,GAAG,EACxB,YAAqB,EACrB,UAAmB;QAEnC,KAAK,CACH,YAAY,OAAO,mBAAmB,UAAU,CAAC,CAAC,CAAC,UAAU,UAAU,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EACjF,2BAA2B,EAC3B,UAAU,CACX,CAAC;QATc,YAAO,GAAP,OAAO,CAAQ;QACf,eAAU,GAAV,UAAU,CAAc;QACxB,iBAAY,GAAZ,YAAY,CAAS;QACrB,eAAU,GAAV,UAAU,CAAS;IAOrC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,CAAC,qDAAqD;IACpE,CAAC;CACF;AAjBD,0DAiBC;AAED;;GAEG;AACH,MAAa,cAAe,SAAQ,kBAAS;IAG3C,YACkB,OAAe,EACf,cAAuB,EACvB,iBAA0B;QAE1C,MAAM,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACxF,KAAK,CACH,oCAAoC,OAAO,GAAG,EAC9C,kBAAkB,EAClB,GAAG,CACJ,CAAC;QATc,YAAO,GAAP,OAAO,CAAQ;QACf,mBAAc,GAAd,cAAc,CAAS;QACvB,sBAAiB,GAAjB,iBAAiB,CAAS;QAQ1C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,CAAC,4CAA4C;IAC3D,CAAC;CACF;AApBD,wCAoBC;AAED;;GAEG;AACH,MAAa,mBAAoB,SAAQ,kBAAS;IAChD,YACkB,MAAc,EACd,UAAkB,EAClB,QAAiB;QAEjC,KAAK,CACH,gCAAgC,UAAU,YAAY,EACtD,uBAAuB,EACvB,GAAG,CACJ,CAAC;QARc,WAAM,GAAN,MAAM,CAAQ;QACd,eAAU,GAAV,UAAU,CAAQ;QAClB,aAAQ,GAAR,QAAQ,CAAS;IAOnC,CAAC;CACF;AAZD,kDAYC;AAED;;GAEG;AACH,MAAa,eAAgB,SAAQ,kBAAS;IAC5C,YACkB,MAAc,EACd,UAAkB,EAClB,aAAoB,EACpB,QAAiB;QAEjC,KAAK,CACH,kCAAkC,UAAU,EAAE,EAC9C,kBAAkB,EAClB,GAAG,CACJ,CAAC;QATc,WAAM,GAAN,MAAM,CAAQ;QACd,eAAU,GAAV,UAAU,CAAQ;QAClB,kBAAa,GAAb,aAAa,CAAO;QACpB,aAAQ,GAAR,QAAQ,CAAS;IAOnC,CAAC;CACF;AAbD,0CAaC;AAED;;GAEG;AACH,MAAa,uBAAwB,SAAQ,kBAAS;IACpD,YACkB,UAAkB,EAClB,WAAmB,EACnB,KAAe;QAE/B,KAAK,CACH,iCAAiC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EACrD,2BAA2B,EAC3B,GAAG,CACJ,CAAC;QARc,eAAU,GAAV,UAAU,CAAQ;QAClB,gBAAW,GAAX,WAAW,CAAQ;QACnB,UAAK,GAAL,KAAK,CAAU;IAOjC,CAAC;CACF;AAZD,0DAYC;AAED;;GAEG;AACH,MAAa,wBAAyB,SAAQ,kBAAS;IACrD,YACkB,WAAmB,EACnB,UAAkB,EAClB,SAAiB,4BAA4B;QAE7D,KAAK,CACH,yBAAyB,MAAM,EAAE,EACjC,4BAA4B,EAC5B,GAAG,CACJ,CAAC;QARc,gBAAW,GAAX,WAAW,CAAQ;QACnB,eAAU,GAAV,UAAU,CAAQ;QAClB,WAAM,GAAN,MAAM,CAAuC;IAO/D,CAAC;CACF;AAZD,4DAYC"}