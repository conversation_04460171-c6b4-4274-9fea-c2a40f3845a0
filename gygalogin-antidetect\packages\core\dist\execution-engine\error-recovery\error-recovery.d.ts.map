{"version": 3, "file": "error-recovery.d.ts", "sourceRoot": "", "sources": ["../../../src/execution-engine/error-recovery/error-recovery.ts"], "names": [], "mappings": "AAaA;;GAEG;<PERSON>CH,<PERSON>AM,WAAW,oBAAoB;IACnC,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,IAAI,CAAC;IAChB,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,CAAC,EAAE,GAAG,EAAE,CAAC;IAClB,cAAc,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACrC,cAAc,CAAC,EAAE,KAAK,EAAE,CAAC;IACzB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG;IAChC,MAAM,EAAE,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,UAAU,CAAC;IAC/C,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,iBAAiB,CAAC,EAAE,GAAG,EAAE,CAAC;IAC1B,kBAAkB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACzC,cAAc,CAAC,EAAE,GAAG,EAAE,CAAC;IACvB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC;IAE1B,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,oBAAoB,GAAG,OAAO,CAAC;IAChE,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;CACpF;AAED;;GAEG;AACH,qBAAa,kCAAmC,YAAW,sBAAsB;IAC/E,QAAQ,CAAC,IAAI,gCAAgC;IAC7C,QAAQ,CAAC,QAAQ,MAAM;IAEvB,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAI1B,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC;CA6BzF;AAED;;GAEG;AACH,qBAAa,yBAA0B,YAAW,sBAAsB;IACtE,QAAQ,CAAC,IAAI,uBAAuB;IACpC,QAAQ,CAAC,QAAQ,MAAM;IAEvB,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAI1B,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC;CA6BzF;AAED;;GAEG;AACH,qBAAa,uBAAwB,YAAW,sBAAsB;IACpE,QAAQ,CAAC,IAAI,qBAAqB;IAClC,QAAQ,CAAC,QAAQ,MAAM;IAEvB,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAK1B,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC;CAwBzF;AAED;;GAEG;AACH,qBAAa,0BAA2B,YAAW,sBAAsB;IACvE,QAAQ,CAAC,IAAI,wBAAwB;IACrC,QAAQ,CAAC,QAAQ,MAAM;IAEvB,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAO1B,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC;CAiCzF;AAED;;GAEG;AACH,qBAAa,8BAA+B,YAAW,sBAAsB;IAC3E,QAAQ,CAAC,IAAI,4BAA4B;IACzC,QAAQ,CAAC,QAAQ,MAAM;IAEvB,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAK1B,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAqCxF,OAAO,CAAC,iBAAiB;IAqBzB,OAAO,CAAC,eAAe;IAYvB,OAAO,CAAC,cAAc;IAItB,OAAO,CAAC,cAAc;CAcvB;AAED;;GAEG;AACH,qBAAa,4BAA6B,YAAW,sBAAsB;IACzE,QAAQ,CAAC,IAAI,0BAA0B;IACvC,QAAQ,CAAC,QAAQ,MAAM;IAEvB,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAQnC;IAEF,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAI1B,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC;CAkBzF;AAED;;GAEG;AACH,qBAAa,wBAAyB,YAAW,sBAAsB;IACrE,QAAQ,CAAC,IAAI,sBAAsB;IACnC,QAAQ,CAAC,QAAQ,MAAM;IAEvB,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;IAI1B,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,oBAAoB,GAAG,OAAO,CAAC,mBAAmB,CAAC;CA0BzF"}