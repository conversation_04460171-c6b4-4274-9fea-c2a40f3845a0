"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecutionTerminatedError = exports.CircularDependencyError = exports.ExpressionError = exports.NodeDependencyError = exports.RateLimitError = exports.ServiceUnavailableError = exports.CredentialError = exports.DataValidationError = exports.RetryableError = exports.WorkflowExecutionError = exports.NodeParameterError = exports.NodeTimeoutError = exports.NodeExecutionError = void 0;
const shared_1 = require("@gygalogin/shared");
/**
 * Base class for all node execution errors
 */
class NodeExecutionError extends shared_1.BaseError {
    constructor(message, nodeId, nodeType, context, attempt, maxAttempts) {
        super(message, 'NODE_EXECUTION_ERROR', 500);
        this.nodeId = nodeId;
        this.nodeType = nodeType;
        this.context = context;
        this.attempt = attempt;
        this.maxAttempts = maxAttempts;
    }
}
exports.NodeExecutionError = NodeExecutionError;
/**
 * Error thrown when node execution times out
 */
class NodeTimeoutError extends shared_1.BaseError {
    constructor(nodeId, timeout, nodeType, context, attempt) {
        super(`Node execution timed out after ${timeout}ms`, 'NODE_TIMEOUT_ERROR', 500);
        this.nodeId = nodeId;
        this.timeout = timeout;
        this.nodeType = nodeType;
        this.attempt = attempt;
        // Always include timeout in context
        this.context = Object.assign({ timeout }, context);
    }
}
exports.NodeTimeoutError = NodeTimeoutError;
/**
 * Error thrown when a node parameter is invalid or missing
 */
class NodeParameterError extends shared_1.BaseError {
    constructor(nodeId, parameterName, expectedType, actualValue, nodeType) {
        super(`Invalid parameter "${parameterName}"${expectedType ? ` (expected ${expectedType})` : ''}`, 'NODE_PARAMETER_ERROR', 400);
        this.nodeId = nodeId;
        this.parameterName = parameterName;
        this.expectedType = expectedType;
        this.actualValue = actualValue;
        this.nodeType = nodeType;
    }
}
exports.NodeParameterError = NodeParameterError;
/**
 * Error thrown when workflow execution fails at the overall level
 */
class WorkflowExecutionError extends shared_1.BaseError {
    constructor(message, executionId, workflowId, phase, context, failedNodeId) {
        super(message, 'WORKFLOW_EXECUTION_ERROR', 500);
        this.executionId = executionId;
        this.workflowId = workflowId;
        this.phase = phase;
        this.context = context;
        this.failedNodeId = failedNodeId;
    }
}
exports.WorkflowExecutionError = WorkflowExecutionError;
/**
 * Error that can be retried with specific retry configuration
 */
class RetryableError extends shared_1.BaseError {
    constructor(message, retryAfter, maxRetries, retryCount, context) {
        super(message, 'RETRYABLE_ERROR', 500);
        this.retryAfter = retryAfter;
        this.maxRetries = maxRetries;
        this.retryCount = retryCount;
        this.context = context;
    }
    get shouldRetry() {
        return this.retryCount !== undefined &&
            this.maxRetries !== undefined &&
            this.retryCount < this.maxRetries;
    }
}
exports.RetryableError = RetryableError;
/**
 * Error thrown when data validation fails
 */
class DataValidationError extends shared_1.BaseError {
    constructor(message, nodeId, dataIndex, fieldPath, expectedSchema, actualData) {
        super(message, 'DATA_VALIDATION_ERROR', 400);
        this.nodeId = nodeId;
        this.dataIndex = dataIndex;
        this.fieldPath = fieldPath;
        this.expectedSchema = expectedSchema;
        this.actualData = actualData;
    }
}
exports.DataValidationError = DataValidationError;
/**
 * Error thrown when credentials are missing or invalid
 */
class CredentialError extends shared_1.BaseError {
    constructor(message, credentialType, nodeId, context) {
        super(message, 'CREDENTIAL_ERROR', 401);
        this.credentialType = credentialType;
        this.nodeId = nodeId;
        this.context = context;
    }
}
exports.CredentialError = CredentialError;
/**
 * Error thrown when external service is unavailable
 */
class ServiceUnavailableError extends shared_1.BaseError {
    constructor(service, statusCode = 503, responseBody, retryAfter) {
        super(`Service "${service}" is unavailable${statusCode ? ` (HTTP ${statusCode})` : ''}`, 'SERVICE_UNAVAILABLE_ERROR', statusCode);
        this.service = service;
        this.statusCode = statusCode;
        this.responseBody = responseBody;
        this.retryAfter = retryAfter;
    }
    get shouldRetry() {
        return true; // Service unavailable errors are typically retryable
    }
}
exports.ServiceUnavailableError = ServiceUnavailableError;
/**
 * Error thrown when rate limiting occurs
 */
class RateLimitError extends shared_1.BaseError {
    constructor(service, rateLimitReset, remainingRequests) {
        const retryAfter = rateLimitReset ? Math.max(rateLimitReset - Date.now(), 1000) : 60000;
        super(`Rate limit exceeded for service "${service}"`, 'RATE_LIMIT_ERROR', 429);
        this.service = service;
        this.rateLimitReset = rateLimitReset;
        this.remainingRequests = remainingRequests;
        this.retryAfter = retryAfter;
    }
    get shouldRetry() {
        return true; // Rate limit errors are typically retryable
    }
}
exports.RateLimitError = RateLimitError;
/**
 * Error thrown when node dependency is missing
 */
class NodeDependencyError extends shared_1.BaseError {
    constructor(nodeId, dependency, nodeType) {
        super(`Missing required dependency "${dependency}" for node`, 'NODE_DEPENDENCY_ERROR', 500);
        this.nodeId = nodeId;
        this.dependency = dependency;
        this.nodeType = nodeType;
    }
}
exports.NodeDependencyError = NodeDependencyError;
/**
 * Error thrown when expression evaluation fails
 */
class ExpressionError extends shared_1.BaseError {
    constructor(nodeId, expression, originalError, nodeType) {
        super(`Failed to evaluate expression: ${expression}`, 'EXPRESSION_ERROR', 500);
        this.nodeId = nodeId;
        this.expression = expression;
        this.originalError = originalError;
        this.nodeType = nodeType;
    }
}
exports.ExpressionError = ExpressionError;
/**
 * Error thrown when workflow has circular dependencies
 */
class CircularDependencyError extends shared_1.BaseError {
    constructor(workflowId, executionId, cycle) {
        super(`Circular dependency detected: ${cycle.join(' -> ')}`, 'CIRCULAR_DEPENDENCY_ERROR', 400);
        this.workflowId = workflowId;
        this.executionId = executionId;
        this.cycle = cycle;
    }
}
exports.CircularDependencyError = CircularDependencyError;
/**
 * Error thrown when execution is terminated by user
 */
class ExecutionTerminatedError extends shared_1.BaseError {
    constructor(executionId, workflowId, reason = 'User requested termination') {
        super(`Execution terminated: ${reason}`, 'EXECUTION_TERMINATED_ERROR', 499);
        this.executionId = executionId;
        this.workflowId = workflowId;
        this.reason = reason;
    }
}
exports.ExecutionTerminatedError = ExecutionTerminatedError;
//# sourceMappingURL=execution-errors.js.map