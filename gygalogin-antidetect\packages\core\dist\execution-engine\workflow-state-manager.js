"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowStateManager = exports.NodeExecutionStatus = void 0;
var NodeExecutionStatus;
(function (NodeExecutionStatus) {
    NodeExecutionStatus["PENDING"] = "pending";
    NodeExecutionStatus["RUNNING"] = "running";
    NodeExecutionStatus["COMPLETED"] = "completed";
    NodeExecutionStatus["FAILED"] = "failed";
    NodeExecutionStatus["SKIPPED"] = "skipped";
})(NodeExecutionStatus || (exports.NodeExecutionStatus = NodeExecutionStatus = {}));
class WorkflowStateManager {
    constructor(workflowId, executionId) {
        this.workflowId = workflowId;
        this.executionId = executionId;
        this.nodeStates = new Map();
        this.waitingNodes = new Map();
    }
    // Node state management
    setNodeStatus(nodeId, status) {
        const currentState = this.nodeStates.get(nodeId) || { status: NodeExecutionStatus.PENDING };
        if (status === NodeExecutionStatus.RUNNING && !currentState.startTime) {
            currentState.startTime = new Date();
        }
        if (status === NodeExecutionStatus.COMPLETED || status === NodeExecutionStatus.FAILED) {
            currentState.endTime = new Date();
        }
        currentState.status = status;
        this.nodeStates.set(nodeId, currentState);
    }
    getNodeStatus(nodeId) {
        var _a;
        return ((_a = this.nodeStates.get(nodeId)) === null || _a === void 0 ? void 0 : _a.status) || NodeExecutionStatus.PENDING;
    }
    getNodeState(nodeId) {
        return this.nodeStates.get(nodeId);
    }
    setNodeOutput(nodeId, output) {
        const currentState = this.nodeStates.get(nodeId) || { status: NodeExecutionStatus.PENDING };
        currentState.output = output;
        currentState.status = NodeExecutionStatus.COMPLETED;
        currentState.endTime = new Date();
        this.nodeStates.set(nodeId, currentState);
    }
    getNodeOutput(nodeId) {
        var _a;
        return (_a = this.nodeStates.get(nodeId)) === null || _a === void 0 ? void 0 : _a.output;
    }
    setNodeError(nodeId, error) {
        const currentState = this.nodeStates.get(nodeId) || { status: NodeExecutionStatus.PENDING };
        currentState.error = error;
        currentState.status = NodeExecutionStatus.FAILED;
        currentState.endTime = new Date();
        this.nodeStates.set(nodeId, currentState);
    }
    // Waiting nodes management (for multi-input nodes)
    initializeWaitingNode(nodeId, requiredInputs) {
        this.waitingNodes.set(nodeId, {
            received: {},
            requiredInputs,
        });
    }
    addNodeInput(nodeId, inputName, data) {
        const waitingNode = this.waitingNodes.get(nodeId);
        if (!waitingNode) {
            return false;
        }
        waitingNode.received[inputName] = data;
        // Check if all required inputs are received
        const allInputsReceived = waitingNode.requiredInputs.every(inputName => waitingNode.received[inputName] !== undefined);
        return allInputsReceived;
    }
    getWaitingNodeInputs(nodeId) {
        const waitingNode = this.waitingNodes.get(nodeId);
        if (!waitingNode) {
            return [];
        }
        // Combine all received inputs
        return Object.values(waitingNode.received).flat();
    }
    removeWaitingNode(nodeId) {
        this.waitingNodes.delete(nodeId);
    }
    isNodeWaiting(nodeId) {
        return this.waitingNodes.has(nodeId);
    }
    // Execution tracking
    startExecution() {
        this.executionStartTime = new Date();
    }
    endExecution() {
        this.executionEndTime = new Date();
    }
    getExecutionDuration() {
        if (!this.executionStartTime) {
            return undefined;
        }
        const endTime = this.executionEndTime || new Date();
        return endTime.getTime() - this.executionStartTime.getTime();
    }
    // Query methods
    getCompletedNodes() {
        const completedNodes = [];
        for (const [nodeId, state] of this.nodeStates) {
            if (state.status === NodeExecutionStatus.COMPLETED) {
                completedNodes.push(nodeId);
            }
        }
        return completedNodes;
    }
    getFailedNodes() {
        const failedNodes = [];
        for (const [nodeId, state] of this.nodeStates) {
            if (state.status === NodeExecutionStatus.FAILED) {
                failedNodes.push(nodeId);
            }
        }
        return failedNodes;
    }
    getRunningNodes() {
        const runningNodes = [];
        for (const [nodeId, state] of this.nodeStates) {
            if (state.status === NodeExecutionStatus.RUNNING) {
                runningNodes.push(nodeId);
            }
        }
        return runningNodes;
    }
    getAllNodeStates() {
        return new Map(this.nodeStates);
    }
    isExecutionComplete() {
        // Check if there are any nodes still running or pending (excluding waiting nodes)
        for (const [nodeId, state] of this.nodeStates) {
            if (state.status === NodeExecutionStatus.RUNNING ||
                (state.status === NodeExecutionStatus.PENDING && !this.isNodeWaiting(nodeId))) {
                return false;
            }
        }
        return true;
    }
    hasErrors() {
        return this.getFailedNodes().length > 0;
    }
    // Debug/logging methods
    getExecutionSummary() {
        return {
            workflowId: this.workflowId,
            executionId: this.executionId,
            startTime: this.executionStartTime,
            endTime: this.executionEndTime,
            duration: this.getExecutionDuration(),
            completedNodes: this.getCompletedNodes().length,
            failedNodes: this.getFailedNodes().length,
            runningNodes: this.getRunningNodes().length,
            totalNodes: this.nodeStates.size,
            hasErrors: this.hasErrors(),
            isComplete: this.isExecutionComplete(),
        };
    }
}
exports.WorkflowStateManager = WorkflowStateManager;
//# sourceMappingURL=workflow-state-manager.js.map