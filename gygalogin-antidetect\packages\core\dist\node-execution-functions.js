"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodeExecuteFunctions = void 0;
const data_manager_1 = require("./execution-engine/data-manager");
const credential_service_1 = require("./services/credential.service");
class NodeExecuteFunctions {
    constructor(context, node, inputData) {
        // Add a public helpers property as required by IExecuteFunctions
        this.helpers = {};
        this.context = context;
        this.node = node;
        this.inputData = inputData;
    }
    getNodeParameter(name, indexOrItemIndexOrValue, defaultValue) {
        const value = this.node.parameters[name];
        if (value !== undefined) {
            return value;
        }
        if (typeof indexOrItemIndexOrValue === 'number') {
            // Handle index-based parameter access (in a full implementation)
            return defaultValue;
        }
        return indexOrItemIndexOrValue !== undefined ? indexOrItemIndexOrValue : defaultValue;
    }
    getContext() {
        // Return the initial context if available, otherwise create a basic execution context
        const initialContext = this.context.getInitialContext();
        if (initialContext) {
            return initialContext;
        }
        // Create a basic execution context with required properties
        return {
            variables: {},
            nodeOutputs: {}
        };
    }
    // Method to get shared data from enhanced execution context
    getSharedData(key) {
        const context = this.context.getInitialContext();
        if (context && context.sharedData) {
            return key ? context.sharedData[key] : context.sharedData;
        }
        return key ? undefined : {};
    }
    // Method to set shared data in enhanced execution context
    setSharedData(key, value) {
        const context = this.context.getInitialContext();
        if (context && context.sharedData) {
            context.sharedData[key] = value;
        }
    }
    // Method to get execution metadata
    getExecutionInfo() {
        const context = this.context.getInitialContext();
        return {
            executionId: context === null || context === void 0 ? void 0 : context.executionId,
            workflowId: context === null || context === void 0 ? void 0 : context.workflowId,
            runMode: context === null || context === void 0 ? void 0 : context.runMode,
        };
    }
    getInputData(itemIndex) {
        if (typeof itemIndex === 'number') {
            return data_manager_1.DataManager.getInputDataByIndex(this.inputData, itemIndex);
        }
        return this.inputData;
    }
    // Enhanced method to get input data from a specific node
    getInputDataFrom(nodeName) {
        const context = this.context.getInitialContext();
        if (!(context === null || context === void 0 ? void 0 : context.nodeOutputs)) {
            return [];
        }
        return data_manager_1.DataManager.getInputDataFrom(context.nodeOutputs, nodeName);
    }
    // Credentials support
    getCredentials(name) {
        return __awaiter(this, void 0, void 0, function* () {
            const credentialService = (0, credential_service_1.getCredentialService)();
            return credentialService.getCredentials(name);
        });
    }
    // Stub implementation for now
    evaluateComparison(_operator, _value1, _value2) {
        // A real implementation would go here
        return false;
    }
    getNode() {
        return this.node;
    }
}
exports.NodeExecuteFunctions = NodeExecuteFunctions;
//# sourceMappingURL=node-execution-functions.js.map