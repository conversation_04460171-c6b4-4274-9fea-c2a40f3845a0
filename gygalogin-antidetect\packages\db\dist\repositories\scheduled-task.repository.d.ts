import { DataSource, Repository } from 'typeorm';
import { ScheduledTaskEntity } from '../entities/scheduled-task.entity';
export interface ScheduledTaskSearchCriteria {
    workflowId?: string;
    nodeName?: string;
    isActive?: boolean;
}
export declare class ScheduledTaskRepository extends Repository<ScheduledTaskEntity> {
    constructor(dataSource: DataSource);
    /**
     * Find entity by ID - added for compatibility with core package
     */
    findById(id: string): Promise<ScheduledTaskEntity | null>;
    /**
     * Find all entities with options - added for compatibility with core package
     */
    findAll(options?: any): Promise<ScheduledTaskEntity[]>;
    /**
     * Create a new scheduled task entity and return it
     */
    createTask(taskData: Partial<ScheduledTaskEntity>): Promise<ScheduledTaskEntity>;
    /**
     * Update scheduled task and return the updated entity
     */
    updateTask(id: string, updateData: Partial<ScheduledTaskEntity>): Promise<ScheduledTaskEntity | null>;
    /**
     * Delete scheduled task by id and return success status
     */
    deleteTask(id: string): Promise<boolean>;
    /**
     * Find scheduled tasks by workflow ID
     */
    findByWorkflowId(workflowId: string): Promise<ScheduledTaskEntity[]>;
    /**
     * Find active scheduled tasks
     */
    findActiveTasks(): Promise<ScheduledTaskEntity[]>;
    /**
     * Find scheduled task by workflow ID and node name
     */
    findByWorkflowAndNode(workflowId: string, nodeName: string): Promise<ScheduledTaskEntity | null>;
    /**
     * Deactivate all scheduled tasks for a workflow
     */
    deactivateByWorkflowId(workflowId: string): Promise<void>;
    /**
     * Delete all scheduled tasks for a workflow
     */
    deleteByWorkflowId(workflowId: string): Promise<void>;
    /**
     * Search scheduled tasks with criteria
     */
    searchTasks(criteria: ScheduledTaskSearchCriteria): Promise<ScheduledTaskEntity[]>;
}
//# sourceMappingURL=scheduled-task.repository.d.ts.map