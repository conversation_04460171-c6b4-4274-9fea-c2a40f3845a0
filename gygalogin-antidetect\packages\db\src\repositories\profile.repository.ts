import { Service } from '@gygalogin/decorators';
import { DataSource, FindManyOptions, Like, Repository } from 'typeorm';
import { ProfileEntity } from '../entities/profile.entity';

export interface ProfileSearchCriteria {
  name?: string;
  tags?: string[];
  isActive?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
  hasProxy?: boolean;
}

@Service()
export class ProfileRepository extends Repository<ProfileEntity> {
  constructor(dataSource: DataSource) {
    super(ProfileEntity, dataSource.manager);
  }

  /**
   * Find entity by ID - added for compatibility with core package
   */
  async findById(id: string): Promise<ProfileEntity | null> {
    return this.findOne({ where: { id } as any });
  }

  /**
   * Create a new profile entity and return it
   */
  async createProfile(profileData: Partial<ProfileEntity>): Promise<ProfileEntity> {
    const profile = this.create(profileData);
    return this.save(profile);
  }

  /**
   * Update profile and return the updated entity
   */
  async updateProfile(
    id: string,
    updateData: Partial<ProfileEntity>
  ): Promise<ProfileEntity | null> {
    await this.update(id, updateData);
    return this.findOneBy({ id } as any);
  }

  /**
   * Delete profile by id and return success status
   */
  async deleteProfile(id: string): Promise<boolean> {
    const result = await this.delete(id);
    return (result.affected ?? 0) > 0;
  }

  /**
   * Find profiles by name (case-insensitive partial match)
   */
  async findByName(name: string): Promise<ProfileEntity[]> {
    return this.find({
      where: {
        name: Like(`%${name}%`),
      },
    });
  }

  /**
   * Search profiles by name (alias for findByName for test compatibility)
   */
  async searchByName(name: string): Promise<ProfileEntity[]> {
    return this.findByName(name);
  }

  /**
   * Find active profiles
   */
  async findActive(): Promise<ProfileEntity[]> {
    return this.findBy({ isActive: true });
  }

  /**
   * Find profiles by tags
   */
  async findByTags(tags: string[]): Promise<ProfileEntity[]> {
    const queryBuilder = this.createQueryBuilder('profile');

    tags.forEach((tag, index) => {
      queryBuilder.orWhere(`profile.tags LIKE :tag${index}`, { [`tag${index}`]: `%${tag}%` });
    });

    return queryBuilder.getMany();
  }

  /**
   * Search profiles with multiple criteria
   */
  async search(
    criteria: ProfileSearchCriteria,
    options?: FindManyOptions<ProfileEntity>
  ): Promise<ProfileEntity[]> {
    const queryBuilder = this.createQueryBuilder('profile');

    if (criteria.name) {
      queryBuilder.andWhere('profile.name LIKE :name', { name: `%${criteria.name}%` });
    }

    if (criteria.isActive !== undefined) {
      queryBuilder.andWhere('profile.isActive = :isActive', { isActive: criteria.isActive });
    }

    if (criteria.createdAfter) {
      queryBuilder.andWhere('profile.createdAt >= :createdAfter', {
        createdAfter: criteria.createdAfter,
      });
    }

    if (criteria.createdBefore) {
      queryBuilder.andWhere('profile.createdAt <= :createdBefore', {
        createdBefore: criteria.createdBefore,
      });
    }

    if (criteria.hasProxy !== undefined) {
      if (criteria.hasProxy) {
        queryBuilder.andWhere('profile.proxyId IS NOT NULL');
      } else {
        queryBuilder.andWhere('profile.proxyId IS NULL');
      }
    }

    if (criteria.tags && criteria.tags.length > 0) {
      const tagConditions = criteria.tags.map((_, index) => `profile.tags LIKE :tag${index}`);
      const tagParams = criteria.tags.reduce(
        (params, tag, index) => {
          params[`tag${index}`] = `%${tag}%`;
          return params;
        },
        {} as Record<string, string>
      );

      queryBuilder.andWhere(`(${tagConditions.join(' OR ')})`, tagParams);
    }

    if (options?.take) {
      queryBuilder.take(options.take);
    }

    if (options?.skip) {
      queryBuilder.skip(options.skip);
    }

    if (options?.order) {
      Object.entries(options.order).forEach(([key, direction]) => {
        queryBuilder.addOrderBy(`profile.${key}`, direction as 'ASC' | 'DESC');
      });
    }

    return queryBuilder.getMany();
  }

  /**
   * Find a single profile with its proxy data
   */
  async findWithProxyById(id: string): Promise<ProfileEntity | null> {
    return this.createQueryBuilder('profile')
      .leftJoinAndSelect('profile.proxy', 'proxy')
      .where('profile.id = :id', { id })
      .getOne();
  }

  /**
   * Find all profiles with their proxy data
   */
  async findWithProxy(): Promise<ProfileEntity[]> {
    return this.createQueryBuilder('profile').leftJoinAndSelect('profile.proxy', 'proxy').getMany();
  }

  /**
   * Find a single profile with its sessions
   */
  async findWithSessionsById(id: string): Promise<ProfileEntity | null> {
    return this.createQueryBuilder('profile')
      .leftJoinAndSelect('profile.sessions', 'session')
      .where('profile.id = :id', { id })
      .orderBy('session.createdAt', 'DESC')
      .getOne();
  }

  /**
   * Find all profiles with their sessions
   */
  async findWithSessions(): Promise<ProfileEntity[]> {
    return this.createQueryBuilder('profile')
      .leftJoinAndSelect('profile.sessions', 'session')
      .orderBy('session.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Update last used timestamp
   */
  async updateLastUsed(id: string): Promise<void> {
    await this.update(id, { lastUsed: new Date() });
  }

  /**
   * Get profiles usage statistics
   */
  async getUsageStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    withProxy: number;
    withoutProxy: number;
    recentlyUsed: number; // within last 7 days
  }> {
    const total = await this.count();
    const active = await this.countBy({ isActive: true });
    const inactive = total - active;

    const withProxyCount = await this.createQueryBuilder('profile')
      .where('profile.proxyId IS NOT NULL')
      .getCount();

    const withoutProxy = total - withProxyCount;

    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentlyUsed = await this.createQueryBuilder('profile')
      .where('profile.lastUsed >= :sevenDaysAgo', { sevenDaysAgo })
      .getCount();

    return {
      total,
      active,
      inactive,
      withProxy: withProxyCount,
      withoutProxy,
      recentlyUsed,
    };
  }
}
