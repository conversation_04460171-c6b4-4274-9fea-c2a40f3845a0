{"version": 3, "file": "error-monitor.d.ts", "sourceRoot": "", "sources": ["../../../src/execution-engine/error-recovery/error-monitor.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAE9D;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,OAAO,EAAE,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,OAAO,CAAC,CAAC;IAC9C,SAAS,EAAE;QACT,KAAK,EAAE,MAAM,CAAC;QACd,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IACF,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;IACjD,OAAO,EAAE,kBAAkB,EAAE,CAAC;CAC/B;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,IAAI,EAAE,KAAK,GAAG,OAAO,GAAG,SAAS,GAAG,OAAO,GAAG,cAAc,GAAG,QAAQ,CAAC;IACxE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC5B,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,KAAK,OAAO,CAAC;CACzD;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAC9B,EAAE,EAAE,MAAM,CAAC;IACX,SAAS,EAAE,IAAI,CAAC;IAChB,KAAK,EAAE,KAAK,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,cAAc,CAAC,EAAE,GAAG,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED;;GAEG;AACH,MAAM,WAAW,UAAU;IACzB,EAAE,EAAE,MAAM,CAAC;IACX,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;IACjD,SAAS,EAAE,IAAI,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;IAChB,WAAW,EAAE,eAAe,EAAE,CAAC;IAC/B,aAAa,EAAE,MAAM,EAAE,CAAC;IACxB,iBAAiB,EAAE,MAAM,EAAE,CAAC;IAC5B,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED;;GAEG;AACH,MAAM,WAAW,UAAU;IACzB,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACrC,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACrC,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACzC,kBAAkB,EAAE,KAAK,CAAC;QACxB,SAAS,EAAE,IAAI,CAAC;QAChB,KAAK,EAAE,MAAM,CAAC;QACd,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACpC,CAAC,CAAC;IACH,mBAAmB,EAAE,MAAM,CAAC;IAC5B,mBAAmB,EAAE,MAAM,CAAC;IAC5B,gBAAgB,EAAE,KAAK,CAAC;QACtB,OAAO,EAAE,MAAM,CAAC;QAChB,KAAK,EAAE,MAAM,CAAC;QACd,cAAc,EAAE,IAAI,CAAC;KACtB,CAAC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,oBAAoB,EAAE,MAAM,CAAC;IAC7B,eAAe,EAAE,MAAM,CAAC;IACxB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,wBAAwB,EAAE,OAAO,CAAC;IAClC,aAAa,EAAE,iBAAiB,EAAE,CAAC;IACnC,cAAc,EAAE,YAAY,EAAE,CAAC;CAChC;AAED;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAChC,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,CAAC,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACzC,SAAS,CAAC,CAAC,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC;CACxC;AAED;;GAEG;AACH,qBAAa,YAAY;IAQX,OAAO,CAAC,MAAM;IAP1B,OAAO,CAAC,WAAW,CAAyB;IAC5C,OAAO,CAAC,QAAQ,CAAsB;IACtC,OAAO,CAAC,MAAM,CAAoB;IAClC,OAAO,CAAC,aAAa,CAA2B;IAChD,OAAO,CAAC,KAAK,CAAsC;IACnD,OAAO,CAAC,eAAe,CAAC,CAAiB;gBAErB,MAAM,EAAE,kBAAkB;IAO9C;;OAEG;IACI,WAAW,CAChB,KAAK,EAAE,KAAK,EACZ,MAAM,EAAE,MAAM,EACd,QAAQ,EAAE,MAAM,EAChB,UAAU,EAAE,MAAM,EAClB,WAAW,EAAE,MAAM,EACnB,cAAc,CAAC,EAAE,GAAG,EACpB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAC7B,IAAI;IA2BP;;OAEG;IACI,mBAAmB,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI;IA8B3D;;OAEG;IACI,UAAU,CAAC,OAAO,EAAE,YAAY,GAAG,IAAI;IAS9C;;OAEG;IACI,aAAa,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO;IAMhD;;OAEG;IACI,WAAW,IAAI,YAAY,EAAE;IAIpC;;OAEG;IACI,oBAAoB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,eAAe,EAAE;IAenF;;OAEG;IACI,QAAQ,IAAI,UAAU;IAI7B;;OAEG;IACI,SAAS,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,UAAU,EAAE;IAK9C;;OAEG;IACI,WAAW,IAAI,IAAI;IAI1B;;OAEG;IACI,eAAe,CAAC,OAAO,EAAE,iBAAiB,GAAG,IAAI;IAIxD;;OAEG;IACI,kBAAkB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO;IAMvD;;OAEG;IACI,cAAc,CAAC,UAAU,CAAC,EAAE,MAAM,GAAG;QAC1C,OAAO,EAAE,UAAU,CAAC;QACpB,SAAS,EAAE,KAAK,CAAC;YAAE,OAAO,EAAE,MAAM,CAAC;YAAC,KAAK,EAAE,MAAM,CAAC;YAAC,KAAK,EAAE,MAAM,EAAE,CAAA;SAAE,CAAC,CAAC;QACtE,eAAe,EAAE,MAAM,EAAE,CAAC;KAC3B;IAiCD;;OAEG;IACI,OAAO,IAAI,IAAI;IAOtB,OAAO,CAAC,yBAAyB;IAuDjC,OAAO,CAAC,iBAAiB;IAMzB,OAAO,CAAC,aAAa;IAcrB,OAAO,CAAC,cAAc;IAStB,OAAO,CAAC,sBAAsB;YAQhB,YAAY;YAuCZ,oBAAoB;IAiBlC,OAAO,CAAC,UAAU;IAmBlB,OAAO,CAAC,8BAA8B;IAiBtC,OAAO,CAAC,uBAAuB;IAoC/B,OAAO,CAAC,WAAW;IAgCnB,OAAO,CAAC,oBAAoB;IAU5B,OAAO,CAAC,eAAe;IAavB,OAAO,CAAC,oBAAoB;IAI5B,OAAO,CAAC,eAAe;CAGxB"}