"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FallbackRecoveryStrategy = exports.NetworkErrorRecoveryStrategy = exports.DataValidationRecoveryStrategy = exports.CredentialRecoveryStrategy = exports.TimeoutRecoveryStrategy = exports.RateLimitRecoveryStrategy = exports.ServiceUnavailableRecoveryStrategy = void 0;
const execution_errors_1 = require("../../errors/execution-errors");
/**
 * Strategy for handling service unavailability errors
 */
class ServiceUnavailableRecoveryStrategy {
    constructor() {
        this.name = 'ServiceUnavailableRecovery';
        this.priority = 80;
    }
    canHandle(error) {
        return error instanceof execution_errors_1.ServiceUnavailableError;
    }
    recover(error, context) {
        return __awaiter(this, void 0, void 0, function* () {
            const serviceError = error;
            // If we have a retry-after header, use it
            if (serviceError.retryAfter) {
                return {
                    action: 'retry',
                    delay: serviceError.retryAfter,
                    reason: `Service unavailable, retrying after ${serviceError.retryAfter}ms`
                };
            }
            // Use exponential backoff based on attempt number
            const baseDelay = 1000;
            const delay = Math.min(baseDelay * Math.pow(2, context.attempt), 30000); // Max 30 seconds
            if (context.attempt < context.maxAttempts) {
                return {
                    action: 'retry',
                    delay,
                    reason: `Service unavailable, exponential backoff (attempt ${context.attempt}/${context.maxAttempts})`
                };
            }
            return {
                action: 'fail',
                reason: 'Service remains unavailable after maximum retry attempts'
            };
        });
    }
}
exports.ServiceUnavailableRecoveryStrategy = ServiceUnavailableRecoveryStrategy;
/**
 * Strategy for handling rate limiting errors
 */
class RateLimitRecoveryStrategy {
    constructor() {
        this.name = 'RateLimitRecovery';
        this.priority = 90;
    }
    canHandle(error) {
        return error instanceof execution_errors_1.RateLimitError;
    }
    recover(error, context) {
        return __awaiter(this, void 0, void 0, function* () {
            const rateLimitError = error;
            // Wait for rate limit reset if available
            if (rateLimitError.rateLimitReset) {
                const resetTime = rateLimitError.rateLimitReset;
                const delay = Math.max(resetTime - Date.now(), 1000);
                return {
                    action: 'retry',
                    delay,
                    reason: `Rate limit exceeded, waiting until reset (${new Date(resetTime).toISOString()})`
                };
            }
            // Use the retry after from the error
            if (rateLimitError.retryAfter) {
                return {
                    action: 'retry',
                    delay: rateLimitError.retryAfter,
                    reason: `Rate limit exceeded, retrying after ${rateLimitError.retryAfter}ms`
                };
            }
            return {
                action: 'fail',
                reason: 'Rate limit exceeded with no reset time available'
            };
        });
    }
}
exports.RateLimitRecoveryStrategy = RateLimitRecoveryStrategy;
/**
 * Strategy for handling timeout errors
 */
class TimeoutRecoveryStrategy {
    constructor() {
        this.name = 'TimeoutRecovery';
        this.priority = 70;
    }
    canHandle(error) {
        return error instanceof execution_errors_1.NodeTimeoutError ||
            error.message.toLowerCase().includes('timeout');
    }
    recover(error, context) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            // For timeout errors, we can try increasing the timeout
            const currentTimeout = ((_a = context.nodeParameters) === null || _a === void 0 ? void 0 : _a.timeout) || 30000;
            const maxTimeout = 300000; // 5 minutes max
            if (currentTimeout < maxTimeout && context.attempt < context.maxAttempts) {
                const newTimeout = Math.min(currentTimeout * 2, maxTimeout);
                return {
                    action: 'retry',
                    delay: 1000, // Small delay before retry
                    modifiedParameters: Object.assign(Object.assign({}, context.nodeParameters), { timeout: newTimeout }),
                    reason: `Timeout occurred, increasing timeout to ${newTimeout}ms for retry`
                };
            }
            return {
                action: 'fail',
                reason: 'Maximum timeout reached or retry attempts exceeded'
            };
        });
    }
}
exports.TimeoutRecoveryStrategy = TimeoutRecoveryStrategy;
/**
 * Strategy for handling credential errors
 */
class CredentialRecoveryStrategy {
    constructor() {
        this.name = 'CredentialRecovery';
        this.priority = 95;
    }
    canHandle(error) {
        return error instanceof execution_errors_1.CredentialError ||
            error.message.toLowerCase().includes('credential') ||
            error.message.toLowerCase().includes('authentication') ||
            error.message.toLowerCase().includes('unauthorized');
    }
    recover(error, context) {
        return __awaiter(this, void 0, void 0, function* () {
            // Credential errors are usually not recoverable through retry
            // But we might want to try alternative credentials or skip the operation
            var _a, _b;
            if ((_a = context.nodeParameters) === null || _a === void 0 ? void 0 : _a.fallbackCredentials) {
                return {
                    action: 'retry',
                    delay: 500,
                    modifiedParameters: Object.assign(Object.assign({}, context.nodeParameters), { credentials: context.nodeParameters.fallbackCredentials }),
                    reason: 'Using fallback credentials for retry'
                };
            }
            // Check if this node can be skipped
            if ((_b = context.nodeParameters) === null || _b === void 0 ? void 0 : _b.continueOnCredentialError) {
                return {
                    action: 'skip',
                    reason: 'Skipping node due to credential error (continueOnCredentialError=true)',
                    fallbackOutput: [{
                            json: { error: 'Credential error', skipped: true },
                            pairedItem: { item: 0 }
                        }]
                };
            }
            return {
                action: 'fail',
                reason: 'Credential error cannot be recovered without valid credentials'
            };
        });
    }
}
exports.CredentialRecoveryStrategy = CredentialRecoveryStrategy;
/**
 * Strategy for handling data validation errors
 */
class DataValidationRecoveryStrategy {
    constructor() {
        this.name = 'DataValidationRecovery';
        this.priority = 60;
    }
    canHandle(error) {
        return error instanceof execution_errors_1.DataValidationError ||
            error.message.toLowerCase().includes('validation');
    }
    recover(error, context) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b;
            const validationError = error;
            // Try to sanitize or fix the data
            if (context.inputData && ((_a = context.nodeParameters) === null || _a === void 0 ? void 0 : _a.autoFixData)) {
                const sanitizedData = this.sanitizeInputData(context.inputData, validationError);
                if (sanitizedData) {
                    return {
                        action: 'retry',
                        delay: 100,
                        modifiedInputData: sanitizedData,
                        reason: 'Retrying with sanitized input data'
                    };
                }
            }
            // If we can't fix the data, check if we should skip or use fallback
            if ((_b = context.nodeParameters) === null || _b === void 0 ? void 0 : _b.skipInvalidData) {
                const validData = this.filterValidData(context.inputData || []);
                if (validData.length > 0) {
                    return {
                        action: 'retry',
                        delay: 100,
                        modifiedInputData: validData,
                        reason: 'Retrying with valid data only (invalid data filtered out)'
                    };
                }
            }
            return {
                action: 'fail',
                reason: 'Data validation error cannot be automatically resolved'
            };
        });
    }
    sanitizeInputData(inputData, validationError) {
        // Basic data sanitization logic
        try {
            return inputData.map(item => {
                var _a;
                if (validationError.fieldPath) {
                    // Try to fix the specific field
                    const fixedItem = Object.assign({}, item);
                    if (((_a = validationError.expectedSchema) === null || _a === void 0 ? void 0 : _a.type) === 'string' &&
                        typeof this.getNestedValue(item, validationError.fieldPath) !== 'string') {
                        this.setNestedValue(fixedItem, validationError.fieldPath, String(this.getNestedValue(item, validationError.fieldPath) || ''));
                    }
                    return fixedItem;
                }
                return item;
            });
        }
        catch (_a) {
            return null;
        }
    }
    filterValidData(inputData) {
        // Filter out invalid data items
        return inputData.filter(item => {
            try {
                // Basic validation - ensure item has json property
                return item && typeof item === 'object' && item.json;
            }
            catch (_a) {
                return false;
            }
        });
    }
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current === null || current === void 0 ? void 0 : current[key], obj);
    }
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        if (!lastKey)
            return;
        const target = keys.reduce((current, key) => {
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            return current[key];
        }, obj);
        target[lastKey] = value;
    }
}
exports.DataValidationRecoveryStrategy = DataValidationRecoveryStrategy;
/**
 * Generic network error recovery strategy
 */
class NetworkErrorRecoveryStrategy {
    constructor() {
        this.name = 'NetworkErrorRecovery';
        this.priority = 50;
        this.networkErrorPatterns = [
            /network/i,
            /connection/i,
            /timeout/i,
            /econnreset/i,
            /econnrefused/i,
            /enotfound/i,
            /socket hang up/i
        ];
    }
    canHandle(error) {
        return this.networkErrorPatterns.some(pattern => pattern.test(error.message));
    }
    recover(error, context) {
        return __awaiter(this, void 0, void 0, function* () {
            if (context.attempt < context.maxAttempts) {
                // Exponential backoff for network errors
                const baseDelay = 2000;
                const delay = Math.min(baseDelay * Math.pow(1.5, context.attempt), 30000);
                return {
                    action: 'retry',
                    delay,
                    reason: `Network error detected, retrying with exponential backoff (attempt ${context.attempt}/${context.maxAttempts})`
                };
            }
            return {
                action: 'fail',
                reason: 'Network error persisted after maximum retry attempts'
            };
        });
    }
}
exports.NetworkErrorRecoveryStrategy = NetworkErrorRecoveryStrategy;
/**
 * Fallback recovery strategy that handles any unhandled retryable errors
 */
class FallbackRecoveryStrategy {
    constructor() {
        this.name = 'FallbackRecovery';
        this.priority = 10; // Lowest priority
    }
    canHandle(error) {
        return error instanceof execution_errors_1.RetryableError || error instanceof execution_errors_1.NodeExecutionError;
    }
    recover(error, context) {
        return __awaiter(this, void 0, void 0, function* () {
            // Check if this is a retryable error
            if (error instanceof execution_errors_1.RetryableError && error.shouldRetry) {
                const delay = error.retryAfter || (1000 * Math.pow(2, context.attempt));
                return {
                    action: 'retry',
                    delay: Math.min(delay, 60000), // Max 1 minute delay
                    reason: `Retryable error detected (${error.constructor.name})`
                };
            }
            // For other node execution errors, try one more time if we haven't exceeded attempts
            if (error instanceof execution_errors_1.NodeExecutionError && context.attempt < context.maxAttempts) {
                return {
                    action: 'retry',
                    delay: 1000 + (context.attempt * 500),
                    reason: `Generic retry for node execution error (attempt ${context.attempt}/${context.maxAttempts})`
                };
            }
            return {
                action: 'fail',
                reason: 'No recovery strategy available for this error type'
            };
        });
    }
}
exports.FallbackRecoveryStrategy = FallbackRecoveryStrategy;
//# sourceMappingURL=error-recovery.js.map