import { INode, INodeExecutionData, Workflow } from '@gygalogin/shared';
import { ExecuteContext } from './node-execution-context/execute-context';
import { WorkflowStateManager } from './workflow-state-manager';
interface IRetrySettings {
    count: number;
    backoff?: 'exponential' | 'linear';
    delay?: number;
}
export interface INodeExecutorOptions {
    timeout?: number;
    retries?: number;
    continueOnFail?: boolean;
    retrySettings?: IRetrySettings;
}
export declare class NodeExecutor {
    private workflow;
    private stateManager;
    private abortSignal?;
    constructor(workflow: Workflow, stateManager: WorkflowStateManager, abortSignal?: AbortSignal | undefined);
    executeNode(node: INode, inputData: INodeExecutionData[], context: ExecuteContext, options?: INodeExecutorOptions): Promise<INodeExecutionData[]>;
    private attemptNodeExecution;
    private handleNodeError;
    private executeWithTimeout;
    private calculateRetryDelay;
    private delay;
    static applyPairedItem(inputData: INodeExecutionData[], outputData: INodeExecutionData[]): INodeExecutionData[];
}
export {};
//# sourceMappingURL=node-executor.d.ts.map