import { Service } from '@gygalogin/decorators';
import { DataSource, FindManyOptions, Repository } from 'typeorm';
import { ExecutionEntity, ExecutionMode, ExecutionStatus } from '../entities/execution.entity';

export interface ExecutionSearchCriteria {
  workflowId?: string;
  status?: ExecutionStatus;
  mode?: ExecutionMode;
  startedAfter?: Date;
  startedBefore?: Date;
  finishedAfter?: Date;
  finishedBefore?: Date;
}

@Service()
export class ExecutionRepository extends Repository<ExecutionEntity> {
  constructor(dataSource: DataSource) {
    super(ExecutionEntity, dataSource.manager);
  }

  /**
   * Find entity by ID - added for compatibility with core package
   */
  async findById(id: string): Promise<ExecutionEntity | null> {
    return this.findOne({ where: { id } as any });
  }

  /**
   * Create a new execution entity and return it
   */
  async createExecution(executionData: Partial<ExecutionEntity>): Promise<ExecutionEntity> {
    const execution = this.create(executionData);
    return this.save(execution);
  }

  /**
   * Update execution and return the updated entity
   */
  async updateExecution(
    id: string,
    updateData: Partial<ExecutionEntity>
  ): Promise<ExecutionEntity | null> {
    await this.update(id, updateData);
    return this.findOneBy({ id } as any);
  }

  /**
   * Delete execution by id and return success status
   */
  async deleteExecution(id: string): Promise<boolean> {
    const result = await this.delete(id);
    return (result.affected ?? 0) > 0;
  }

  /**
   * Find executions by workflow ID
   */
  async findByWorkflowId(workflowId: string, limit?: number): Promise<ExecutionEntity[]> {
    const query = this.createQueryBuilder('execution')
      .where('execution.workflowId = :workflowId', { workflowId })
      .orderBy('execution.startedAt', 'DESC');

    if (limit) {
      query.take(limit);
    }

    return query.getMany();
  }

  /**
   * Find executions by status
   */
  async findByStatus(status: ExecutionStatus): Promise<ExecutionEntity[]> {
    return this.findBy({ status });
  }

  /**
   * Find running executions
   */
  async findRunning(): Promise<ExecutionEntity[]> {
    return this.findByStatus(ExecutionStatus.RUNNING);
  }

  /**
   * Find executions that have been running for too long (possibly stuck)
   */
  async findStuckExecutions(timeoutMinutes: number = 60): Promise<ExecutionEntity[]> {
    const timeoutThreshold = new Date();
    timeoutThreshold.setMinutes(timeoutThreshold.getMinutes() - timeoutMinutes);

    return this.createQueryBuilder('execution')
      .where('execution.status = :status', { status: ExecutionStatus.RUNNING })
      .andWhere('execution.startedAt < :threshold', { threshold: timeoutThreshold })
      .getMany();
  }

  /**
   * Search executions with multiple criteria
   */
  async search(
    criteria: ExecutionSearchCriteria,
    options?: FindManyOptions<ExecutionEntity>
  ): Promise<ExecutionEntity[]> {
    const queryBuilder = this.createQueryBuilder('execution');

    if (criteria.workflowId) {
      queryBuilder.andWhere('execution.workflowId = :workflowId', {
        workflowId: criteria.workflowId,
      });
    }

    if (criteria.status) {
      queryBuilder.andWhere('execution.status = :status', { status: criteria.status });
    }

    if (criteria.mode) {
      queryBuilder.andWhere('execution.mode = :mode', { mode: criteria.mode });
    }

    if (criteria.startedAfter) {
      queryBuilder.andWhere('execution.startedAt >= :startedAfter', {
        startedAfter: criteria.startedAfter,
      });
    }

    if (criteria.startedBefore) {
      queryBuilder.andWhere('execution.startedAt <= :startedBefore', {
        startedBefore: criteria.startedBefore,
      });
    }

    if (criteria.finishedAfter) {
      queryBuilder.andWhere('execution.finishedAt >= :finishedAfter', {
        finishedAfter: criteria.finishedAfter,
      });
    }

    if (criteria.finishedBefore) {
      queryBuilder.andWhere('execution.finishedAt <= :finishedBefore', {
        finishedBefore: criteria.finishedBefore,
      });
    }

    if (options?.take) {
      queryBuilder.take(options.take);
    }

    if (options?.skip) {
      queryBuilder.skip(options.skip);
    }

    // Default order by startedAt DESC
    queryBuilder.orderBy('execution.startedAt', 'DESC');

    if (options?.order) {
      queryBuilder.orderBy({});
      Object.entries(options.order).forEach(([key, direction]) => {
        queryBuilder.addOrderBy(`execution.${key}`, direction as 'ASC' | 'DESC');
      });
    }

    return queryBuilder.getMany();
  }

  /**
   * Start a new execution
   */
  async startExecution(
    workflowId: string,
    mode: ExecutionMode = ExecutionMode.MANUAL
  ): Promise<ExecutionEntity> {
    const execution = this.create({
      workflowId,
      status: ExecutionStatus.RUNNING,
      mode,
      startedAt: new Date(),
      data: {
        resultData: {},
        executionData: {},
      },
    });

    return this.save(execution);
  }

  /**
   * Mark execution as successful
   */
  async markAsSuccess(id: string, resultData?: any): Promise<ExecutionEntity | null> {
    const updateData: any = {
      status: ExecutionStatus.SUCCESS,
      finishedAt: new Date(),
    };

    if (resultData) {
      updateData.data = resultData;
    }

    await this.update(id, updateData);
    return this.findOneBy({ id } as any);
  }

  /**
   * Mark execution as failed
   */
  async markAsError(id: string, error: string, errorData?: any): Promise<ExecutionEntity | null> {
    const updateData: any = {
      status: ExecutionStatus.ERROR,
      finishedAt: new Date(),
      error,
    };

    if (errorData) {
      updateData.data = errorData;
    }

    await this.update(id, updateData);
    return this.findOneBy({ id } as any);
  }

  /**
   * Mark execution as cancelled
   */
  async markAsCancelled(id: string): Promise<ExecutionEntity | null> {
    await this.update(id, {
      status: ExecutionStatus.CANCELED,
      finishedAt: new Date(),
    });
    return this.findOneBy({ id } as any);
  }

  /**
   * Update execution data during runtime
   */
  async updateExecutionData(id: string, data: any): Promise<void> {
    await this.update(id, { data });
  }

  /**
   * Get execution statistics
   */
  async getStats(timeframeHours: number = 24): Promise<{
    total: number;
    running: number;
    success: number;
    error: number;
    cancelled: number;
    averageExecutionTime: number; // in milliseconds
    executionsInTimeframe: number;
  }> {
    const total = await this.count();
    const running = await this.countBy({ status: ExecutionStatus.RUNNING });
    const success = await this.countBy({ status: ExecutionStatus.SUCCESS });
    const error = await this.countBy({ status: ExecutionStatus.ERROR });
    const cancelled = await this.countBy({ status: ExecutionStatus.CANCELED });

    const timeframeStart = new Date();
    timeframeStart.setHours(timeframeStart.getHours() - timeframeHours);

    const executionsInTimeframe = await this.createQueryBuilder('execution')
      .where('execution.startedAt >= :timeframeStart', { timeframeStart })
      .getCount();

    // Calculate average execution time for completed executions
    const completedExecutions = await this.createQueryBuilder('execution')
      .select(
        "AVG(CAST(strftime('%s', execution.finishedAt) AS INTEGER) - CAST(strftime('%s', execution.startedAt) AS INTEGER))",
        'avgTime'
      )
      .where('execution.status IN (:...statuses)', {
        statuses: [ExecutionStatus.SUCCESS, ExecutionStatus.ERROR],
      })
      .andWhere('execution.finishedAt IS NOT NULL')
      .getRawOne();

    const averageExecutionTime = completedExecutions.avgTime
      ? parseFloat(completedExecutions.avgTime) * 1000
      : 0;

    return {
      total,
      running,
      success,
      error,
      cancelled,
      averageExecutionTime,
      executionsInTimeframe,
    };
  }

  /**
   * Clean up old executions
   */
  async cleanupOldExecutions(daysToKeep: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const result = await this.createQueryBuilder()
      .delete()
      .where('startedAt < :cutoffDate', { cutoffDate })
      .andWhere('status != :running', { running: ExecutionStatus.RUNNING })
      .execute();

    return result.affected || 0;
  }

  /**
   * Find execution with workflow details
   */
  async findWithWorkflow(id: string): Promise<ExecutionEntity | null> {
    return this.createQueryBuilder('execution')
      .leftJoinAndSelect('execution.workflow', 'workflow')
      .where('execution.id = :id', { id })
      .getOne();
  }
}
