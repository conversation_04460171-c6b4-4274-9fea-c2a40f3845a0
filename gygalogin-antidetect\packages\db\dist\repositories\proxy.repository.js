"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProxyRepository = void 0;
const decorators_1 = require("@gygalogin/decorators");
const typeorm_1 = require("typeorm");
const proxy_entity_1 = require("../entities/proxy.entity");
let ProxyRepository = class ProxyRepository extends typeorm_1.Repository {
    constructor(dataSource) {
        super(proxy_entity_1.ProxyEntity, dataSource.manager);
    }
    /**
     * Find entity by ID - added for compatibility with core package
     */
    async findById(id) {
        return this.findOne({ where: { id } });
    }
    /**
     * Create a new proxy entity and return it
     */
    async createProxy(proxyData) {
        const proxy = this.create(proxyData);
        return this.save(proxy);
    }
    /**
     * Update proxy and return the updated entity
     */
    async updateProxy(id, updateData) {
        await this.update(id, updateData);
        return this.findOneBy({ id });
    }
    /**
     * Delete proxy by id and return success status
     */
    async deleteProxy(id) {
        const result = await this.delete(id);
        return (result.affected ?? 0) > 0;
    }
    /**
     * Find all proxies
     */
    async findAll() {
        return this.find();
    }
    /**
     * Find enabled proxies
     */
    async findEnabled() {
        return this.findBy({ enabled: true });
    }
    /**
     * Find proxies by type
     */
    async findByType(type) {
        return this.findBy({ type });
    }
    /**
     * Find proxies by country
     */
    async findByCountry(country) {
        return this.findBy({ country });
    }
    /**
     * Find proxies by host
     */
    async findByHost(host) {
        return this.findBy({ host });
    }
    /**
     * Find healthy proxies with minimum health score
     */
    async findHealthy(minHealthScore = 70) {
        return this.find({
            where: {
                healthScore: (0, typeorm_1.MoreThan)(minHealthScore),
                enabled: true,
            },
        });
    }
    /**
     * Find non-blacklisted proxies
     */
    async findNonBlacklisted() {
        const now = new Date();
        return this.createQueryBuilder('proxy')
            .where('proxy.enabled = :enabled', { enabled: true })
            .andWhere('(proxy.blacklistedUntil IS NULL OR proxy.blacklistedUntil <= :now)', { now })
            .getMany();
    }
    /**
     * Search proxies with multiple criteria
     */
    async search(criteria) {
        const queryBuilder = this.createQueryBuilder('proxy');
        if (criteria.type) {
            queryBuilder.andWhere('proxy.type = :type', { type: criteria.type });
        }
        if (criteria.country) {
            queryBuilder.andWhere('proxy.country = :country', { country: criteria.country });
        }
        if (criteria.city) {
            queryBuilder.andWhere('proxy.city = :city', { city: criteria.city });
        }
        if (criteria.enabled !== undefined) {
            queryBuilder.andWhere('proxy.enabled = :enabled', { enabled: criteria.enabled });
        }
        if (criteria.minHealthScore !== undefined) {
            queryBuilder.andWhere('proxy.healthScore >= :minHealthScore', {
                minHealthScore: criteria.minHealthScore,
            });
        }
        if (criteria.isBlacklisted === false) {
            const now = new Date();
            queryBuilder.andWhere('(proxy.blacklistedUntil IS NULL OR proxy.blacklistedUntil <= :now)', {
                now,
            });
        }
        else if (criteria.isBlacklisted === true) {
            const now = new Date();
            queryBuilder.andWhere('proxy.blacklistedUntil > :now', { now });
        }
        return queryBuilder.getMany();
    }
    /**
     * Get the best available proxy based on health score and blacklist status
     */
    async getBestAvailable(type) {
        const queryBuilder = this.createQueryBuilder('proxy')
            .where('proxy.enabled = :enabled', { enabled: true })
            .andWhere('(proxy.blacklistedUntil IS NULL OR proxy.blacklistedUntil <= :now)', {
            now: new Date(),
        })
            .orderBy('proxy.healthScore', 'DESC')
            .limit(1);
        if (type) {
            queryBuilder.andWhere('proxy.type = :type', { type });
        }
        return queryBuilder.getOne();
    }
    /**
     * Update health score
     */
    async updateHealthScore(id, healthScore) {
        await this.update(id, {
            healthScore,
            lastChecked: new Date(),
        });
    }
    /**
     * Blacklist a proxy until a specific date
     */
    async blacklist(id, until) {
        await this.update(id, { blacklistedUntil: until });
    }
    /**
     * Remove blacklist from a proxy
     */
    async removeBlacklist(id) {
        await this.query('UPDATE proxies SET blacklistedUntil = NULL WHERE id = ?', [id]);
    }
    /**
     * Find proxies with their associated profiles
     */
    async findWithProfiles(id) {
        const query = this.createQueryBuilder('proxy').leftJoinAndSelect('proxy.profiles', 'profile');
        if (id) {
            query.where('proxy.id = :id', { id });
            return query.getOne();
        }
        return query.getMany();
    }
    /**
     * Get proxy statistics
     */
    async getStats() {
        const total = await this.count();
        const enabled = await this.countBy({ enabled: true });
        const disabled = total - enabled;
        const healthy = await this.createQueryBuilder('proxy')
            .where('proxy.healthScore >= :minScore', { minScore: 70 })
            .getCount();
        const unhealthy = total - healthy;
        const now = new Date();
        const blacklisted = await this.createQueryBuilder('proxy')
            .where('proxy.blacklistedUntil > :now', { now })
            .getCount();
        // Get stats by type
        const typeStats = await this.createQueryBuilder('proxy')
            .select('proxy.type', 'type')
            .addSelect('COUNT(*)', 'count')
            .groupBy('proxy.type')
            .getRawMany();
        const byType = typeStats.reduce((acc, stat) => {
            acc[stat.type] = parseInt(stat.count);
            return acc;
        }, {});
        // Get stats by country
        const countryStats = await this.createQueryBuilder('proxy')
            .select('proxy.country', 'country')
            .addSelect('COUNT(*)', 'count')
            .where('proxy.country IS NOT NULL')
            .groupBy('proxy.country')
            .getRawMany();
        const byCountry = countryStats.reduce((acc, stat) => {
            if (stat.country) {
                acc[stat.country] = parseInt(stat.count);
            }
            return acc;
        }, {});
        return {
            total,
            enabled,
            disabled,
            healthy,
            unhealthy,
            blacklisted,
            byType,
            byCountry,
        };
    }
};
exports.ProxyRepository = ProxyRepository;
exports.ProxyRepository = ProxyRepository = __decorate([
    (0, decorators_1.Service)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], ProxyRepository);
//# sourceMappingURL=proxy.repository.js.map