import { Service } from '@gygalogin/decorators';
import { DataSource, Repository } from 'typeorm';
import { ScheduledTaskEntity } from '../entities/scheduled-task.entity';

export interface ScheduledTaskSearchCriteria {
  workflowId?: string;
  nodeName?: string;
  isActive?: boolean;
}

@Service()
export class ScheduledTaskRepository extends Repository<ScheduledTaskEntity> {
  constructor(dataSource: DataSource) {
    super(ScheduledTaskEntity, dataSource.manager);
  }

  /**
   * Find entity by ID - added for compatibility with core package
   */
  async findById(id: string): Promise<ScheduledTaskEntity | null> {
    return this.findOne({ where: { id } as any });
  }

  /**
   * Find all entities with options - added for compatibility with core package
   */
  async findAll(options?: any): Promise<ScheduledTaskEntity[]> {
    return this.find(options);
  }

  /**
   * Create a new scheduled task entity and return it
   */
  async createTask(taskData: Partial<ScheduledTaskEntity>): Promise<ScheduledTaskEntity> {
    const task = this.create(taskData);
    return this.save(task);
  }

  /**
   * Update scheduled task and return the updated entity
   */
  async updateTask(
    id: string,
    updateData: Partial<ScheduledTaskEntity>
  ): Promise<ScheduledTaskEntity | null> {
    await this.update(id, updateData);
    return this.findOneBy({ id } as any);
  }

  /**
   * Delete scheduled task by id and return success status
   */
  async deleteTask(id: string): Promise<boolean> {
    const result = await this.delete(id);
    return (result.affected ?? 0) > 0;
  }

  /**
   * Find scheduled tasks by workflow ID
   */
  async findByWorkflowId(workflowId: string): Promise<ScheduledTaskEntity[]> {
    return this.find({
      where: { workflowId },
      relations: ['workflow'],
    });
  }

  /**
   * Find active scheduled tasks
   */
  async findActiveTasks(): Promise<ScheduledTaskEntity[]> {
    return this.find({
      where: { isActive: true },
      relations: ['workflow'],
    });
  }

  /**
   * Find scheduled task by workflow ID and node name
   */
  async findByWorkflowAndNode(
    workflowId: string,
    nodeName: string
  ): Promise<ScheduledTaskEntity | null> {
    return this.findOne({
      where: { workflowId, nodeName },
      relations: ['workflow'],
    });
  }

  /**
   * Deactivate all scheduled tasks for a workflow
   */
  async deactivateByWorkflowId(workflowId: string): Promise<void> {
    await this.update({ workflowId }, { isActive: false });
  }

  /**
   * Delete all scheduled tasks for a workflow
   */
  async deleteByWorkflowId(workflowId: string): Promise<void> {
    await this.delete({ workflowId });
  }

  /**
   * Search scheduled tasks with criteria
   */
  async searchTasks(criteria: ScheduledTaskSearchCriteria): Promise<ScheduledTaskEntity[]> {
    const queryBuilder = this.createQueryBuilder('scheduledTask').leftJoinAndSelect(
      'scheduledTask.workflow',
      'workflow'
    );

    if (criteria.workflowId) {
      queryBuilder.andWhere('scheduledTask.workflowId = :workflowId', {
        workflowId: criteria.workflowId,
      });
    }

    if (criteria.nodeName) {
      queryBuilder.andWhere('scheduledTask.nodeName = :nodeName', { nodeName: criteria.nodeName });
    }

    if (criteria.isActive !== undefined) {
      queryBuilder.andWhere('scheduledTask.isActive = :isActive', { isActive: criteria.isActive });
    }

    return queryBuilder.getMany();
  }
}
