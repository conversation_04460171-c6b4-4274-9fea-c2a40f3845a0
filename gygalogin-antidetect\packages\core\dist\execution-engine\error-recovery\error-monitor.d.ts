import { ErrorRecoveryEvent } from './error-recovery-manager';
/**
 * Error pattern for detecting recurring issues
 */
export interface ErrorPattern {
    id: string;
    name: string;
    description: string;
    pattern: RegExp | ((error: Error) => boolean);
    threshold: {
        count: number;
        timeWindow: number;
    };
    severity: 'low' | 'medium' | 'high' | 'critical';
    actions: ErrorPatternAction[];
}
/**
 * Action to take when an error pattern is detected
 */
export interface ErrorPatternAction {
    type: 'log' | 'alert' | 'webhook' | 'email' | 'disable_node' | 'custom';
    config: Record<string, any>;
    condition?: (occurrences: ErrorOccurrence[]) => boolean;
}
/**
 * Occurrence of an error
 */
export interface ErrorOccurrence {
    id: string;
    timestamp: Date;
    error: Error;
    nodeId: string;
    nodeType: string;
    workflowId: string;
    executionId: string;
    recoveryResult?: any;
    metadata?: Record<string, any>;
}
/**
 * Alert generated when error pattern threshold is exceeded
 */
export interface ErrorAlert {
    id: string;
    patternId: string;
    patternName: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    timestamp: Date;
    message: string;
    occurrences: ErrorOccurrence[];
    affectedNodes: string[];
    affectedWorkflows: string[];
    recommendations?: string[];
    metadata?: Record<string, any>;
}
/**
 * Error statistics for monitoring
 */
export interface ErrorStats {
    totalErrors: number;
    errorsByType: Record<string, number>;
    errorsByNode: Record<string, number>;
    errorsByWorkflow: Record<string, number>;
    errorsByTimeWindow: Array<{
        timestamp: Date;
        count: number;
        errorTypes: Record<string, number>;
    }>;
    recoverySuccessRate: number;
    averageRecoveryTime: number;
    topErrorPatterns: Array<{
        pattern: string;
        count: number;
        lastOccurrence: Date;
    }>;
}
/**
 * Configuration for error monitoring
 */
export interface ErrorMonitorConfig {
    maxOccurrenceHistory: number;
    cleanupInterval: number;
    defaultTimeWindow: number;
    enableRealTimeMonitoring: boolean;
    alertHandlers: ErrorAlertHandler[];
    customPatterns: ErrorPattern[];
}
/**
 * Handler for error alerts
 */
export interface ErrorAlertHandler {
    name: string;
    handle(alert: ErrorAlert): Promise<void>;
    canHandle?(alert: ErrorAlert): boolean;
}
/**
 * Error monitoring and alerting system
 */
export declare class ErrorMonitor {
    private config;
    private occurrences;
    private patterns;
    private alerts;
    private alertHandlers;
    private stats;
    private cleanupInterval?;
    constructor(config: ErrorMonitorConfig);
    /**
     * Record an error occurrence
     */
    recordError(error: Error, nodeId: string, nodeType: string, workflowId: string, executionId: string, recoveryResult?: any, metadata?: Record<string, any>): void;
    /**
     * Listen to error recovery events
     */
    handleRecoveryEvent(event: ErrorRecoveryEvent): void;
    /**
     * Add a custom error pattern
     */
    addPattern(pattern: ErrorPattern): void;
    /**
     * Remove an error pattern
     */
    removePattern(patternId: string): boolean;
    /**
     * Get all error patterns
     */
    getPatterns(): ErrorPattern[];
    /**
     * Get recent error occurrences
     */
    getRecentOccurrences(limit?: number, timeWindow?: number): ErrorOccurrence[];
    /**
     * Get error statistics
     */
    getStats(): ErrorStats;
    /**
     * Get recent alerts
     */
    getAlerts(limit?: number): ErrorAlert[];
    /**
     * Clear all alerts
     */
    clearAlerts(): void;
    /**
     * Add an alert handler
     */
    addAlertHandler(handler: ErrorAlertHandler): void;
    /**
     * Remove an alert handler
     */
    removeAlertHandler(handlerName: string): boolean;
    /**
     * Generate error summary report
     */
    generateReport(timeWindow?: number): {
        summary: ErrorStats;
        topErrors: Array<{
            message: string;
            count: number;
            nodes: string[];
        }>;
        recommendations: string[];
    };
    /**
     * Cleanup resources
     */
    destroy(): void;
    private initializeDefaultPatterns;
    private addCustomPatterns;
    private checkPatterns;
    private patternMatches;
    private getMatchingOccurrences;
    private triggerAlert;
    private executePatternAction;
    private logMessage;
    private generatePatternRecommendations;
    private generateRecommendations;
    private updateStats;
    private startCleanupInterval;
    private initializeStats;
    private generateOccurrenceId;
    private generateAlertId;
}
//# sourceMappingURL=error-monitor.d.ts.map