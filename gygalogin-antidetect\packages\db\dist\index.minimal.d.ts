import 'reflect-metadata';
import { DataSource, Repository } from 'typeorm';
import { AppDataSource } from './connection';
import { ProfileEntity } from './entities/profile.entity';
import { ProxyEntity } from './entities/proxy.entity';
import { SessionEntity } from './entities/session.entity';
import { WorkflowEntity } from './entities/workflow.entity';
import { ExecutionEntity } from './entities/execution.entity';
import { ScheduledTaskEntity } from './entities/scheduled-task.entity';
import { BaseRepository } from './repositories/base.repository';
declare class SessionRepository extends BaseRepository<SessionEntity> {
    constructor(repository: Repository<SessionEntity>);
}
declare class MinimalProfileRepository extends Repository<ProfileEntity> {
    constructor(dataSource: DataSource);
    findByName(name: string): Promise<ProfileEntity[]>;
    findActive(): Promise<ProfileEntity[]>;
}
export declare class DB {
    private profileRepository;
    private sessionRepository;
    private _dataSource;
    constructor(dataSource: DataSource);
    initialize(): Promise<void>;
    close(): Promise<void>;
    get profiles(): MinimalProfileRepository;
    get sessions(): SessionRepository;
    get dataSource(): DataSource;
}
export declare const database: DB;
export { ProfileEntity, ProxyEntity, SessionEntity, WorkflowEntity, ExecutionEntity, ScheduledTaskEntity };
export { BaseRepository, MinimalProfileRepository };
export { AppDataSource };
export { ExecutionStatus, ExecutionMode } from './entities/execution.entity';
//# sourceMappingURL=index.minimal.d.ts.map