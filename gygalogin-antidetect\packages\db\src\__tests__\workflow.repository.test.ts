import { WorkflowRepository } from '../repositories/workflow.repository';
import { testDataSource } from './setup';

let workflowRepo: WorkflowRepository;

beforeAll(async () => {
  workflowRepo = new WorkflowRepository(testDataSource);
});

describe('WorkflowRepository', () => {
  it('should be defined', () => {
    expect(workflowRepo).toBeDefined();
  });

  describe('create', () => {
    it('should create a new workflow', async () => {
      const workflowData = {
        name: 'Test Workflow',
        description: 'A test workflow',
        nodes: [
          {
            id: 'node1',
            name: 'Start Node',
            type: 'start',
            typeVersion: 1,
            position: [100, 100] as [number, number],
            parameters: {},
          },
        ],
        connections: [
          {
            sourceNodeId: 'node1',
            sourceOutput: 'main',
            targetNodeId: 'node2',
            targetInput: 'main',
          },
        ],
        active: true,
      };

      const workflow = await workflowRepo.createWorkflow(workflowData);

      expect(workflow.id).toBeDefined();
      expect(workflow.name).toBe('Test Workflow');
      expect(workflow.description).toBe('A test workflow');
      expect(workflow.active).toBe(true);
      expect(workflow.nodes).toHaveLength(1);
      expect(workflow.connections).toHaveLength(1);
    });
  });

  describe('findById', () => {
    it('should find a workflow by ID using new findById method', async () => {
      const workflowData = {
        name: 'Find Test Workflow',
        nodes: [],
        connections: [],
        active: false,
      };

      const createdWorkflow = await workflowRepo.createWorkflow(workflowData);
      const foundWorkflow = await workflowRepo.findById(createdWorkflow.id);

      expect(foundWorkflow).toBeDefined();
      expect(foundWorkflow?.id).toBe(createdWorkflow.id);
      expect(foundWorkflow?.name).toBe('Find Test Workflow');
    });

    it('should return null for non-existent workflow using findById', async () => {
      const workflow = await workflowRepo.findById('non-existent-id');
      expect(workflow).toBeNull();
    });

    it('should find a workflow by ID using legacy findOneBy method', async () => {
      const workflowData = {
        name: 'Legacy Find Test Workflow',
        nodes: [],
        connections: [],
        active: false,
      };

      const createdWorkflow = await workflowRepo.createWorkflow(workflowData);
      const foundWorkflow = await workflowRepo.findOneBy({ id: createdWorkflow.id } as any);

      expect(foundWorkflow).toBeDefined();
      expect(foundWorkflow?.id).toBe(createdWorkflow.id);
      expect(foundWorkflow?.name).toBe('Legacy Find Test Workflow');
    });
  });

  describe('findByName', () => {
    it('should find workflows by name', async () => {
      const workflowData = {
        name: 'Named Workflow',
        nodes: [],
        connections: [],
        active: true,
      };

      await workflowRepo.createWorkflow(workflowData);
      const workflows = await workflowRepo.findByName('Named Workflow');

      expect(workflows).toHaveLength(1);
      expect(workflows[0].name).toBe('Named Workflow');
    });
  });

  describe('findActive', () => {
    it('should find only active workflows', async () => {
      await workflowRepo.createWorkflow({
        name: 'Active Workflow',
        nodes: [],
        connections: [],
        active: true,
      });

      await workflowRepo.createWorkflow({
        name: 'Inactive Workflow',
        nodes: [],
        connections: [],
        active: false,
      });

      const activeWorkflows = await workflowRepo.findActive();

      expect(activeWorkflows.length).toBeGreaterThan(0);
      activeWorkflows.forEach(workflow => {
        expect(workflow.active).toBe(true);
      });
    });
  });

  describe('activate/deactivate', () => {
    it('should activate a workflow', async () => {
      const workflow = await workflowRepo.createWorkflow({
        name: 'Test Activation',
        nodes: [],
        connections: [],
        active: false,
      });

      const activatedWorkflow = await workflowRepo.activate(workflow.id);

      expect(activatedWorkflow).toBeDefined();
      expect(activatedWorkflow?.active).toBe(true);
    });

    it('should deactivate a workflow', async () => {
      const workflow = await workflowRepo.createWorkflow({
        name: 'Test Deactivation',
        nodes: [],
        connections: [],
        active: true,
      });

      const deactivatedWorkflow = await workflowRepo.deactivate(workflow.id);

      expect(deactivatedWorkflow).toBeDefined();
      expect(deactivatedWorkflow?.active).toBe(false);
    });
  });

  describe('update', () => {
    it('should update workflow data', async () => {
      const workflow = await workflowRepo.createWorkflow({
        name: 'Original Name',
        nodes: [],
        connections: [],
        active: false,
      });

      const updatedWorkflow = await workflowRepo.updateWorkflow(workflow.id, {
        name: 'Updated Name',
        description: 'Updated description',
        active: true,
      });

      expect(updatedWorkflow).toBeDefined();
      expect(updatedWorkflow?.name).toBe('Updated Name');
      expect(updatedWorkflow?.description).toBe('Updated description');
      expect(updatedWorkflow?.active).toBe(true);
    });
  });

  describe('delete', () => {
    it('should delete a workflow', async () => {
      const workflow = await workflowRepo.createWorkflow({
        name: 'To Delete',
        nodes: [],
        connections: [],
        active: false,
      });

      const result = await workflowRepo.deleteWorkflow(workflow.id);
      expect(result).toBe(true);

      const deletedWorkflow = await workflowRepo.findOneBy({ id: workflow.id } as any);
      expect(deletedWorkflow).toBeNull();
    });
  });
});
