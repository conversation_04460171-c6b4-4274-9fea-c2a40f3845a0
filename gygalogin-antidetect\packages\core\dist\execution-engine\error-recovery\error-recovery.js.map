{"version": 3, "file": "error-recovery.js", "sourceRoot": "", "sources": ["../../../src/execution-engine/error-recovery/error-recovery.ts"], "names": [], "mappings": ";AAAA,uDAAuD;;;;;;;;;;;;AAGvD,oEAQuC;AA4CvC;;GAEG;AACH,MAAa,kCAAkC;IAA/C;QACW,SAAI,GAAG,4BAA4B,CAAC;QACpC,aAAQ,GAAG,EAAE,CAAC;IAmCzB,CAAC;IAjCC,SAAS,CAAC,KAAY;QACpB,OAAO,KAAK,YAAY,0CAAuB,CAAC;IAClD,CAAC;IAEK,OAAO,CAAC,KAAY,EAAE,OAA6B;;YACvD,MAAM,YAAY,GAAG,KAAgC,CAAC;YAEtD,0CAA0C;YAC1C,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC5B,OAAO;oBACL,MAAM,EAAE,OAAO;oBACf,KAAK,EAAE,YAAY,CAAC,UAAU;oBAC9B,MAAM,EAAE,uCAAuC,YAAY,CAAC,UAAU,IAAI;iBAC3E,CAAC;YACJ,CAAC;YAED,kDAAkD;YAClD,MAAM,SAAS,GAAG,IAAI,CAAC;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,iBAAiB;YAE1F,IAAI,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBAC1C,OAAO;oBACL,MAAM,EAAE,OAAO;oBACf,KAAK;oBACL,MAAM,EAAE,qDAAqD,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,GAAG;iBACvG,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,0DAA0D;aACnE,CAAC;QACJ,CAAC;KAAA;CACF;AArCD,gFAqCC;AAED;;GAEG;AACH,MAAa,yBAAyB;IAAtC;QACW,SAAI,GAAG,mBAAmB,CAAC;QAC3B,aAAQ,GAAG,EAAE,CAAC;IAmCzB,CAAC;IAjCC,SAAS,CAAC,KAAY;QACpB,OAAO,KAAK,YAAY,iCAAc,CAAC;IACzC,CAAC;IAEK,OAAO,CAAC,KAAY,EAAE,OAA6B;;YACvD,MAAM,cAAc,GAAG,KAAuB,CAAC;YAE/C,yCAAyC;YACzC,IAAI,cAAc,CAAC,cAAc,EAAE,CAAC;gBAClC,MAAM,SAAS,GAAG,cAAc,CAAC,cAAc,CAAC;gBAChD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,CAAC;gBAErD,OAAO;oBACL,MAAM,EAAE,OAAO;oBACf,KAAK;oBACL,MAAM,EAAE,6CAA6C,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,GAAG;iBAC1F,CAAC;YACJ,CAAC;YAED,qCAAqC;YACrC,IAAI,cAAc,CAAC,UAAU,EAAE,CAAC;gBAC9B,OAAO;oBACL,MAAM,EAAE,OAAO;oBACf,KAAK,EAAE,cAAc,CAAC,UAAU;oBAChC,MAAM,EAAE,uCAAuC,cAAc,CAAC,UAAU,IAAI;iBAC7E,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,kDAAkD;aAC3D,CAAC;QACJ,CAAC;KAAA;CACF;AArCD,8DAqCC;AAED;;GAEG;AACH,MAAa,uBAAuB;IAApC;QACW,SAAI,GAAG,iBAAiB,CAAC;QACzB,aAAQ,GAAG,EAAE,CAAC;IA+BzB,CAAC;IA7BC,SAAS,CAAC,KAAY;QACpB,OAAO,KAAK,YAAY,mCAAgB;YACjC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IACzD,CAAC;IAEK,OAAO,CAAC,KAAY,EAAE,OAA6B;;;YACvD,wDAAwD;YACxD,MAAM,cAAc,GAAG,CAAA,MAAA,OAAO,CAAC,cAAc,0CAAE,OAAO,KAAI,KAAK,CAAC;YAChE,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,gBAAgB;YAE3C,IAAI,cAAc,GAAG,UAAU,IAAI,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACzE,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC;gBAE5D,OAAO;oBACL,MAAM,EAAE,OAAO;oBACf,KAAK,EAAE,IAAI,EAAE,2BAA2B;oBACxC,kBAAkB,kCACb,OAAO,CAAC,cAAc,KACzB,OAAO,EAAE,UAAU,GACpB;oBACD,MAAM,EAAE,2CAA2C,UAAU,cAAc;iBAC5E,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,oDAAoD;aAC7D,CAAC;QACJ,CAAC;KAAA;CACF;AAjCD,0DAiCC;AAED;;GAEG;AACH,MAAa,0BAA0B;IAAvC;QACW,SAAI,GAAG,oBAAoB,CAAC;QAC5B,aAAQ,GAAG,EAAE,CAAC;IA0CzB,CAAC;IAxCC,SAAS,CAAC,KAAY;QACpB,OAAO,KAAK,YAAY,kCAAe;YAChC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;YAClD,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC;YACtD,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;IAC9D,CAAC;IAEK,OAAO,CAAC,KAAY,EAAE,OAA6B;;YACvD,8DAA8D;YAC9D,yEAAyE;;YAEzE,IAAI,MAAA,OAAO,CAAC,cAAc,0CAAE,mBAAmB,EAAE,CAAC;gBAChD,OAAO;oBACL,MAAM,EAAE,OAAO;oBACf,KAAK,EAAE,GAAG;oBACV,kBAAkB,kCACb,OAAO,CAAC,cAAc,KACzB,WAAW,EAAE,OAAO,CAAC,cAAc,CAAC,mBAAmB,GACxD;oBACD,MAAM,EAAE,sCAAsC;iBAC/C,CAAC;YACJ,CAAC;YAED,oCAAoC;YACpC,IAAI,MAAA,OAAO,CAAC,cAAc,0CAAE,yBAAyB,EAAE,CAAC;gBACtD,OAAO;oBACL,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,wEAAwE;oBAChF,cAAc,EAAE,CAAC;4BACf,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,OAAO,EAAE,IAAI,EAAE;4BAClD,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;yBACxB,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,gEAAgE;aACzE,CAAC;QACJ,CAAC;KAAA;CACF;AA5CD,gEA4CC;AAED;;GAEG;AACH,MAAa,8BAA8B;IAA3C;QACW,SAAI,GAAG,wBAAwB,CAAC;QAChC,aAAQ,GAAG,EAAE,CAAC;IA+FzB,CAAC;IA7FC,SAAS,CAAC,KAAY;QACpB,OAAO,KAAK,YAAY,sCAAmB;YACpC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IAC5D,CAAC;IAEK,OAAO,CAAC,KAAY,EAAE,OAA6B;;;YACvD,MAAM,eAAe,GAAG,KAA4B,CAAC;YAErD,kCAAkC;YAClC,IAAI,OAAO,CAAC,SAAS,KAAI,MAAA,OAAO,CAAC,cAAc,0CAAE,WAAW,CAAA,EAAE,CAAC;gBAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;gBAEjF,IAAI,aAAa,EAAE,CAAC;oBAClB,OAAO;wBACL,MAAM,EAAE,OAAO;wBACf,KAAK,EAAE,GAAG;wBACV,iBAAiB,EAAE,aAAa;wBAChC,MAAM,EAAE,oCAAoC;qBAC7C,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,oEAAoE;YACpE,IAAI,MAAA,OAAO,CAAC,cAAc,0CAAE,eAAe,EAAE,CAAC;gBAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;gBAEhE,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACzB,OAAO;wBACL,MAAM,EAAE,OAAO;wBACf,KAAK,EAAE,GAAG;wBACV,iBAAiB,EAAE,SAAS;wBAC5B,MAAM,EAAE,2DAA2D;qBACpE,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,wDAAwD;aACjE,CAAC;QACJ,CAAC;KAAA;IAEO,iBAAiB,CAAC,SAAgB,EAAE,eAAoC;QAC9E,gCAAgC;QAChC,IAAI,CAAC;YACH,OAAO,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;;gBAC1B,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;oBAC9B,gCAAgC;oBAChC,MAAM,SAAS,qBAAQ,IAAI,CAAE,CAAC;oBAC9B,IAAI,CAAA,MAAA,eAAe,CAAC,cAAc,0CAAE,IAAI,MAAK,QAAQ;wBACjD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,SAAS,CAAC,KAAK,QAAQ,EAAE,CAAC;wBAC7E,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC,SAAS,EACtD,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;oBACxE,CAAC;oBACD,OAAO,SAAS,CAAC;gBACnB,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,WAAM,CAAC;YACP,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,SAAgB;QACtC,gCAAgC;QAChC,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAC7B,IAAI,CAAC;gBACH,mDAAmD;gBACnD,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC;YACvD,CAAC;YAAC,WAAM,CAAC;gBACP,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,GAAQ,EAAE,IAAY;QAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IACvE,CAAC;IAEO,cAAc,CAAC,GAAQ,EAAE,IAAY,EAAE,KAAU;QACvD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;YAC1C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACtD,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YACpB,CAAC;YACD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,MAAM,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;IAC1B,CAAC;CACF;AAjGD,wEAiGC;AAED;;GAEG;AACH,MAAa,4BAA4B;IAAzC;QACW,SAAI,GAAG,sBAAsB,CAAC;QAC9B,aAAQ,GAAG,EAAE,CAAC;QAEN,yBAAoB,GAAG;YACtC,UAAU;YACV,aAAa;YACb,UAAU;YACV,aAAa;YACb,eAAe;YACf,YAAY;YACZ,iBAAiB;SAClB,CAAC;IAwBJ,CAAC;IAtBC,SAAS,CAAC,KAAY;QACpB,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IAChF,CAAC;IAEK,OAAO,CAAC,KAAY,EAAE,OAA6B;;YACvD,IAAI,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBAC1C,yCAAyC;gBACzC,MAAM,SAAS,GAAG,IAAI,CAAC;gBACvB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC;gBAE1E,OAAO;oBACL,MAAM,EAAE,OAAO;oBACf,KAAK;oBACL,MAAM,EAAE,sEAAsE,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,GAAG;iBACxH,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,sDAAsD;aAC/D,CAAC;QACJ,CAAC;KAAA;CACF;AApCD,oEAoCC;AAED;;GAEG;AACH,MAAa,wBAAwB;IAArC;QACW,SAAI,GAAG,kBAAkB,CAAC;QAC1B,aAAQ,GAAG,EAAE,CAAC,CAAC,kBAAkB;IAgC5C,CAAC;IA9BC,SAAS,CAAC,KAAY;QACpB,OAAO,KAAK,YAAY,iCAAc,IAAI,KAAK,YAAY,qCAAkB,CAAC;IAChF,CAAC;IAEK,OAAO,CAAC,KAAY,EAAE,OAA6B;;YACvD,qCAAqC;YACrC,IAAI,KAAK,YAAY,iCAAc,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;gBACzD,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;gBAExE,OAAO;oBACL,MAAM,EAAE,OAAO;oBACf,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,qBAAqB;oBACpD,MAAM,EAAE,6BAA6B,KAAK,CAAC,WAAW,CAAC,IAAI,GAAG;iBAC/D,CAAC;YACJ,CAAC;YAED,qFAAqF;YACrF,IAAI,KAAK,YAAY,qCAAkB,IAAI,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACjF,OAAO;oBACL,MAAM,EAAE,OAAO;oBACf,KAAK,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC;oBACrC,MAAM,EAAE,mDAAmD,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,GAAG;iBACrG,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,oDAAoD;aAC7D,CAAC;QACJ,CAAC;KAAA;CACF;AAlCD,4DAkCC"}