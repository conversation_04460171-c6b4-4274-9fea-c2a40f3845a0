/**
 * Context information for error recovery operations
 */
export interface ErrorRecoveryContext {
    nodeId: string;
    nodeType: string;
    workflowId: string;
    executionId: string;
    attempt: number;
    maxAttempts: number;
    startTime: Date;
    elapsedTime: number;
    inputData?: any[];
    nodeParameters?: Record<string, any>;
    previousErrors?: Error[];
    metadata?: Record<string, any>;
}
/**
 * Result of error recovery attempt
 */
export type ErrorRecoveryResult = {
    action: 'retry' | 'skip' | 'fail' | 'fallback';
    delay?: number;
    modifiedInputData?: any[];
    modifiedParameters?: Record<string, any>;
    fallbackOutput?: any[];
    reason?: string;
    metadata?: Record<string, any>;
};
/**
 * Interface for error recovery strategies
 */
export interface IErrorRecoveryStrategy {
    readonly name: string;
    readonly priority: number;
    canHandle(error: Error, context: ErrorRecoveryContext): boolean;
    recover(error: Error, context: ErrorRecoveryContext): Promise<ErrorRecoveryResult>;
}
/**
 * Strategy for handling service unavailability errors
 */
export declare class ServiceUnavailableRecoveryStrategy implements IErrorRecoveryStrategy {
    readonly name = "ServiceUnavailableRecovery";
    readonly priority = 80;
    canHandle(error: Error): boolean;
    recover(error: Error, context: ErrorRecoveryContext): Promise<ErrorRecoveryResult>;
}
/**
 * Strategy for handling rate limiting errors
 */
export declare class RateLimitRecoveryStrategy implements IErrorRecoveryStrategy {
    readonly name = "RateLimitRecovery";
    readonly priority = 90;
    canHandle(error: Error): boolean;
    recover(error: Error, context: ErrorRecoveryContext): Promise<ErrorRecoveryResult>;
}
/**
 * Strategy for handling timeout errors
 */
export declare class TimeoutRecoveryStrategy implements IErrorRecoveryStrategy {
    readonly name = "TimeoutRecovery";
    readonly priority = 70;
    canHandle(error: Error): boolean;
    recover(error: Error, context: ErrorRecoveryContext): Promise<ErrorRecoveryResult>;
}
/**
 * Strategy for handling credential errors
 */
export declare class CredentialRecoveryStrategy implements IErrorRecoveryStrategy {
    readonly name = "CredentialRecovery";
    readonly priority = 95;
    canHandle(error: Error): boolean;
    recover(error: Error, context: ErrorRecoveryContext): Promise<ErrorRecoveryResult>;
}
/**
 * Strategy for handling data validation errors
 */
export declare class DataValidationRecoveryStrategy implements IErrorRecoveryStrategy {
    readonly name = "DataValidationRecovery";
    readonly priority = 60;
    canHandle(error: Error): boolean;
    recover(error: Error, context: ErrorRecoveryContext): Promise<ErrorRecoveryResult>;
    private sanitizeInputData;
    private filterValidData;
    private getNestedValue;
    private setNestedValue;
}
/**
 * Generic network error recovery strategy
 */
export declare class NetworkErrorRecoveryStrategy implements IErrorRecoveryStrategy {
    readonly name = "NetworkErrorRecovery";
    readonly priority = 50;
    private readonly networkErrorPatterns;
    canHandle(error: Error): boolean;
    recover(error: Error, context: ErrorRecoveryContext): Promise<ErrorRecoveryResult>;
}
/**
 * Fallback recovery strategy that handles any unhandled retryable errors
 */
export declare class FallbackRecoveryStrategy implements IErrorRecoveryStrategy {
    readonly name = "FallbackRecovery";
    readonly priority = 10;
    canHandle(error: Error): boolean;
    recover(error: Error, context: ErrorRecoveryContext): Promise<ErrorRecoveryResult>;
}
//# sourceMappingURL=error-recovery.d.ts.map