import { DatabaseService } from './database.service';
import { Logger } from '@gygalogin/shared';
export declare class MigrationService {
    private readonly databaseService;
    private readonly logger;
    constructor(databaseService: DatabaseService, logger: Logger);
    runMigrations(): Promise<void>;
    revertLastMigration(): Promise<void>;
    showMigrations(): Promise<void>;
}
//# sourceMappingURL=migration.service.d.ts.map