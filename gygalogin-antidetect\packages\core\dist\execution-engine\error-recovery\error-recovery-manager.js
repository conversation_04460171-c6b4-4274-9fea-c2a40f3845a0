"use strict";
/* eslint-disable @typescript-eslint/no-explicit-any */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorRecoveryManager = void 0;
const error_recovery_1 = require("./error-recovery");
/**
 * Manager for coordinating error recovery strategies
 */
class ErrorRecoveryManager {
    constructor(config = {}) {
        this.config = config;
        this.strategies = [];
        this.stats = {
            totalAttempts: 0,
            successfulRecoveries: 0,
            failedRecoveries: 0,
            strategiesUsed: {},
            totalRecoveryTime: 0,
            averageRecoveryTime: 0
        };
        this.eventListeners = [];
        this.initializeDefaultStrategies();
        this.addCustomStrategies();
    }
    /**
     * Add an event listener for recovery events
     */
    onRecoveryEvent(listener) {
        this.eventListeners.push(listener);
    }
    /**
     * Remove an event listener
     */
    removeRecoveryEventListener(listener) {
        const index = this.eventListeners.indexOf(listener);
        if (index > -1) {
            this.eventListeners.splice(index, 1);
        }
    }
    /**
     * Attempt to recover from an error using available strategies
     */
    attemptRecovery(error, node, context) {
        return __awaiter(this, void 0, void 0, function* () {
            const recoveryContext = {
                nodeId: node.id,
                nodeType: node.type,
                workflowId: context.workflowId || 'unknown',
                executionId: context.executionId || 'unknown',
                attempt: context.attempt || 0,
                maxAttempts: context.maxAttempts || 3,
                startTime: context.startTime || new Date(),
                elapsedTime: context.elapsedTime || 0,
                inputData: context.inputData,
                nodeParameters: node.parameters,
                previousErrors: context.previousErrors || [],
                metadata: context.metadata || {}
            };
            const startTime = Date.now();
            this.stats.totalAttempts++;
            // Emit recovery started event
            this.emitEvent({
                type: 'recovery_started',
                timestamp: new Date(),
                nodeId: node.id,
                error
            });
            try {
                // Check if we've exceeded maximum recovery time
                if (this.config.maxRecoveryTime && recoveryContext.elapsedTime > this.config.maxRecoveryTime) {
                    const result = {
                        action: 'fail',
                        reason: `Maximum recovery time (${this.config.maxRecoveryTime}ms) exceeded`
                    };
                    this.handleRecoveryResult(result, error, node.id, startTime, false);
                    return result;
                }
                // Check if we've exceeded global retry limit
                if (this.config.maxGlobalRetries && recoveryContext.attempt >= this.config.maxGlobalRetries) {
                    const result = {
                        action: 'fail',
                        reason: `Maximum global retries (${this.config.maxGlobalRetries}) exceeded`
                    };
                    this.handleRecoveryResult(result, error, node.id, startTime, false);
                    return result;
                }
                // Find applicable recovery strategies, sorted by priority (highest first)
                const applicableStrategies = this.strategies
                    .filter(strategy => this.isStrategyEnabled(strategy.name))
                    .filter(strategy => strategy.canHandle(error, recoveryContext))
                    .sort((a, b) => b.priority - a.priority);
                if (applicableStrategies.length === 0) {
                    const result = {
                        action: 'fail',
                        reason: 'No applicable recovery strategies found'
                    };
                    this.handleRecoveryResult(result, error, node.id, startTime, false);
                    return result;
                }
                // Try each strategy until one provides a recovery action
                for (const strategy of applicableStrategies) {
                    try {
                        const result = yield strategy.recover(error, recoveryContext);
                        // Track strategy usage
                        this.stats.strategiesUsed[strategy.name] = (this.stats.strategiesUsed[strategy.name] || 0) + 1;
                        // Emit strategy applied event
                        this.emitEvent({
                            type: 'strategy_applied',
                            timestamp: new Date(),
                            nodeId: node.id,
                            error,
                            strategy: strategy.name,
                            result
                        });
                        // If strategy suggests an action other than fail, consider it successful
                        const isSuccessful = result.action !== 'fail';
                        this.handleRecoveryResult(result, error, node.id, startTime, isSuccessful);
                        return result;
                    }
                    catch (strategyError) {
                        // If strategy itself fails, continue to next strategy
                        if (this.config.logRecoveryAttempts) {
                            console.warn(`Recovery strategy ${strategy.name} failed:`, strategyError);
                        }
                        continue;
                    }
                }
                // If all strategies failed to provide a recovery action
                const result = {
                    action: 'fail',
                    reason: 'All recovery strategies failed to provide a recovery action'
                };
                this.handleRecoveryResult(result, error, node.id, startTime, false);
                return result;
            }
            catch (recoveryError) {
                const errorMessage = recoveryError instanceof Error ? recoveryError.message : String(recoveryError);
                const result = {
                    action: 'fail',
                    reason: `Recovery process failed: ${errorMessage}`
                };
                this.handleRecoveryResult(result, error, node.id, startTime, false);
                return result;
            }
        });
    }
    /**
     * Add a custom recovery strategy
     */
    addStrategy(strategy) {
        // Remove existing strategy with same name if it exists
        this.strategies = this.strategies.filter(s => s.name !== strategy.name);
        this.strategies.push(strategy);
        // Sort by priority
        this.strategies.sort((a, b) => b.priority - a.priority);
    }
    /**
     * Remove a recovery strategy by name
     */
    removeStrategy(name) {
        const initialLength = this.strategies.length;
        this.strategies = this.strategies.filter(s => s.name !== name);
        return this.strategies.length < initialLength;
    }
    /**
     * Get list of available strategies
     */
    getStrategies() {
        return [...this.strategies];
    }
    /**
     * Get recovery statistics
     */
    getStats() {
        return Object.assign({}, this.stats);
    }
    /**
     * Reset recovery statistics
     */
    resetStats() {
        this.stats = {
            totalAttempts: 0,
            successfulRecoveries: 0,
            failedRecoveries: 0,
            strategiesUsed: {},
            totalRecoveryTime: 0,
            averageRecoveryTime: 0
        };
    }
    /**
     * Update configuration
     */
    updateConfig(config) {
        this.config = Object.assign(Object.assign({}, this.config), config);
    }
    initializeDefaultStrategies() {
        const defaultStrategies = [
            new error_recovery_1.RateLimitRecoveryStrategy(),
            new error_recovery_1.CredentialRecoveryStrategy(),
            new error_recovery_1.ServiceUnavailableRecoveryStrategy(),
            new error_recovery_1.TimeoutRecoveryStrategy(),
            new error_recovery_1.DataValidationRecoveryStrategy(),
            new error_recovery_1.NetworkErrorRecoveryStrategy(),
            new error_recovery_1.FallbackRecoveryStrategy()
        ];
        defaultStrategies.forEach(strategy => this.addStrategy(strategy));
    }
    addCustomStrategies() {
        if (this.config.customStrategies) {
            this.config.customStrategies.forEach(strategy => this.addStrategy(strategy));
        }
    }
    isStrategyEnabled(strategyName) {
        // If specific strategies are enabled, only use those
        if (this.config.enabledStrategies && this.config.enabledStrategies.length > 0) {
            return this.config.enabledStrategies.includes(strategyName);
        }
        // If specific strategies are disabled, exclude those
        if (this.config.disabledStrategies && this.config.disabledStrategies.length > 0) {
            return !this.config.disabledStrategies.includes(strategyName);
        }
        // Default: all strategies enabled
        return true;
    }
    handleRecoveryResult(result, error, nodeId, startTime, isSuccessful) {
        const elapsedTime = Date.now() - startTime;
        // Update statistics
        this.stats.totalRecoveryTime += elapsedTime;
        this.stats.averageRecoveryTime = this.stats.totalRecoveryTime / this.stats.totalAttempts;
        if (isSuccessful) {
            this.stats.successfulRecoveries++;
        }
        else {
            this.stats.failedRecoveries++;
        }
        // Emit completion event
        this.emitEvent({
            type: isSuccessful ? 'recovery_completed' : 'recovery_failed',
            timestamp: new Date(),
            nodeId,
            error,
            result,
            elapsedTime
        });
        // Log if enabled
        if (this.config.logRecoveryAttempts) {
            const status = isSuccessful ? 'SUCCESS' : 'FAILED';
            console.log(`[ErrorRecovery] ${status}: ${result.action} - ${result.reason} (${elapsedTime}ms)`);
        }
    }
    emitEvent(event) {
        this.eventListeners.forEach(listener => {
            try {
                listener(event);
            }
            catch (error) {
                console.error('Error recovery event listener failed:', error);
            }
        });
    }
}
exports.ErrorRecoveryManager = ErrorRecoveryManager;
//# sourceMappingURL=error-recovery-manager.js.map