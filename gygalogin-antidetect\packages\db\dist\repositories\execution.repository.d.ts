import { DataSource, FindManyOptions, Repository } from 'typeorm';
import { ExecutionEntity, ExecutionMode, ExecutionStatus } from '../entities/execution.entity';
export interface ExecutionSearchCriteria {
    workflowId?: string;
    status?: ExecutionStatus;
    mode?: ExecutionMode;
    startedAfter?: Date;
    startedBefore?: Date;
    finishedAfter?: Date;
    finishedBefore?: Date;
}
export declare class ExecutionRepository extends Repository<ExecutionEntity> {
    constructor(dataSource: DataSource);
    /**
     * Find entity by ID - added for compatibility with core package
     */
    findById(id: string): Promise<ExecutionEntity | null>;
    /**
     * Create a new execution entity and return it
     */
    createExecution(executionData: Partial<ExecutionEntity>): Promise<ExecutionEntity>;
    /**
     * Update execution and return the updated entity
     */
    updateExecution(id: string, updateData: Partial<ExecutionEntity>): Promise<ExecutionEntity | null>;
    /**
     * Delete execution by id and return success status
     */
    deleteExecution(id: string): Promise<boolean>;
    /**
     * Find executions by workflow ID
     */
    findByWorkflowId(workflowId: string, limit?: number): Promise<ExecutionEntity[]>;
    /**
     * Find executions by status
     */
    findByStatus(status: ExecutionStatus): Promise<ExecutionEntity[]>;
    /**
     * Find running executions
     */
    findRunning(): Promise<ExecutionEntity[]>;
    /**
     * Find executions that have been running for too long (possibly stuck)
     */
    findStuckExecutions(timeoutMinutes?: number): Promise<ExecutionEntity[]>;
    /**
     * Search executions with multiple criteria
     */
    search(criteria: ExecutionSearchCriteria, options?: FindManyOptions<ExecutionEntity>): Promise<ExecutionEntity[]>;
    /**
     * Start a new execution
     */
    startExecution(workflowId: string, mode?: ExecutionMode): Promise<ExecutionEntity>;
    /**
     * Mark execution as successful
     */
    markAsSuccess(id: string, resultData?: any): Promise<ExecutionEntity | null>;
    /**
     * Mark execution as failed
     */
    markAsError(id: string, error: string, errorData?: any): Promise<ExecutionEntity | null>;
    /**
     * Mark execution as cancelled
     */
    markAsCancelled(id: string): Promise<ExecutionEntity | null>;
    /**
     * Update execution data during runtime
     */
    updateExecutionData(id: string, data: any): Promise<void>;
    /**
     * Get execution statistics
     */
    getStats(timeframeHours?: number): Promise<{
        total: number;
        running: number;
        success: number;
        error: number;
        cancelled: number;
        averageExecutionTime: number;
        executionsInTimeframe: number;
    }>;
    /**
     * Clean up old executions
     */
    cleanupOldExecutions(daysToKeep?: number): Promise<number>;
    /**
     * Find execution with workflow details
     */
    findWithWorkflow(id: string): Promise<ExecutionEntity | null>;
}
//# sourceMappingURL=execution.repository.d.ts.map