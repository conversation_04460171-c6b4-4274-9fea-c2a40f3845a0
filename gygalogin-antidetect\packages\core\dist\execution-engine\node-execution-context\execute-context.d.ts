import { IDataObject, INodeExecutionData, IExecutionContext, WorkflowExecuteMode } from '@gygalogin/shared';
import { BaseExecuteContext } from './base-execute-context';
export interface IEnhancedExecutionContext extends IExecutionContext {
    executionId?: string;
    workflowId?: string;
    runMode?: WorkflowExecuteMode;
    sharedData: IDataObject;
}
export declare class ExecuteContext extends BaseExecuteContext {
    protected data: IEnhancedExecutionContext;
    constructor(executionId?: string, workflowId?: string, runMode?: WorkflowExecuteMode, initialContext?: IExecutionContext);
    setNodeOutput(nodeId: string, data: INodeExecutionData[]): void;
    getNodeOutput(nodeId: string): INodeExecutionData[] | undefined;
    getExecutionId(): string | undefined;
    getWorkflowId(): string | undefined;
    getRunMode(): WorkflowExecuteMode | undefined;
    setSharedData(key: string, value: any): void;
    getSharedData(key?: string): any;
    getEnhancedContext(): IEnhancedExecutionContext;
}
//# sourceMappingURL=execute-context.d.ts.map